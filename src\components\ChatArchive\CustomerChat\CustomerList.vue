<!-- TODO：
固定搜索框的宽度 -->
<template>
    <div class="mainContainer-customerlist">
        <div class="search" style="width: 800px;">
            <el-input placeholder="搜索客户" v-model="searchWord" clearable class="search-input">
                <template #prefix>
                    <el-icon class="search-icon">
                        <Search />
                    </el-icon>
                </template>
            </el-input>
        </div>

        <div class="lable-navigator">
            <el-tabs v-model="activeName" class="customerList-tabs" @tab-click="handleClick">
                <el-tab-pane label="按客户" name="first">
                    <div class="customer-card" v-for="customer in archivedCustomer" :key="customer.name"
                        @click="handlecustomerClick(customer)">
                        <div class="profile">
                            <img class="staff-avatar" :src="customer.avatar" alt="人员头像">
                        </div>
                        <div class="info">
                            <p class="name">{{ customer.name }}</p>
                            <p :class="['lable', handleCustomerType(customer.type)]">{{ getCustomerCorpName(customer) }}
                            </p>
                        </div>
                        <!-- <div class="mark">
                            <el-tag size="small" type="primary" effect="plain"></el-tag>
                        </div> -->
                    </div>
                </el-tab-pane>
                <!-- <el-tab-pane label="Config" name="second">Config</el-tab-pane>
                <el-tab-pane label="Role" name="third">Role</el-tab-pane>
                <el-tab-pane label="Task" name="fourth">Task</el-tab-pane> -->
            </el-tabs>
        </div>

        <div class="pageContainer">
            <el-pagination v-model:current-page="currentPage" background :size="size" layout="total, prev, pager, next"
                :total="totalCoustomers" :page-size="pageSize" :pager-count="3" small
                @current-change="handlePageChange">
            </el-pagination>
        </div>
    </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue';
import { ref, onMounted, watch, inject, computed } from 'vue';
import axiosInstance from '@/axiosConfig.js'; // 引入 axios 实例
import { ElMessage } from 'element-plus';

const searchWord = ref(''); // searchWord 控制搜索框
const activeName = ref('first') // activeName 控制 tab 切换

const customerList = ref([]); // customerList 人员列表
const totalCoustomers = ref('');//customerList 人员总数

const currentPage = ref(1); // currentPage 当前页码
const pageSize = ref(100); // pageSize 每页显示数量

const currentComponent_List = inject('currentComponent_List')
const currentComponent_ChatList = inject('currentComponent_ChatList')

// -------------------------------------------------------------------------- 获取存档人员列表与搜索实现
const getCustomerList = (curPage, maxPageNum, searchName) => {

    currentComponent_List.value = 'CustomerList';
    currentComponent_ChatList.value = 'CustomerChatList';

    // 获取存档人员列表
    const jwt_token = localStorage.getItem('access_token')

    axiosInstance.post('/api/chatmessage/chatter', {
        type: "customer", // List_search_type 列表搜索类型
        page: curPage,
        limit: maxPageNum,
        name: searchName
    }, {
        headers: {
            Authorization: 'Bearer ' + jwt_token
        },
    },).then(res => {
        // console.log(res.data.staffList.length);
        if (res.data.code === 0) {
            // console.log('获取人员列表成功', res.data.data.result)
            customerList.value = res.data.data.result;
            //此处的数量是所获取的人员列表的数量，非存档员工列表数量
            totalCoustomers.value = res.data.data.total;
            // console.log('totalCoustomers为',totalCoustomers.value)
        }


    }).catch(error => {
        console.log(error);
        ElMessage.error('获取客户列表失败，请检查网络或联系管理员');
    });
}

// 计算属性：过滤
const archivedCustomer = computed(() => {
    // console.log('已过滤存档员工')
    // return customerList.value.filter(customer => customer.sessionArchiveFlag === 1);
    return customerList.value
});

// 更新总数计算
watch(archivedCustomer, (newVal) => {
    console.log('当页客户总数', newVal.length)
    // totalCoustomers.value = newVal.length;
});




//页码变化时触发
const handlePageChange = (page) => {
    currentPage.value = page;
    getCustomerList(currentPage.value, pageSize.value, searchWord.value);
}
//搜索框变化时触发
const filterCustomers = () => {
    if (searchWord.value) {
        currentPage.value = 1; // 重置为第一页
    }
    getCustomerList(currentPage.value, pageSize.value, searchWord.value);
}

onMounted(() => getCustomerList(1, 100, ''));
watch(searchWord, filterCustomers);

// -------------------------------------------------------------------------- 点击员工卡片，传递员工信息（名称，头像）至 CustomerChatList 组件

// 提供给其他组件使用的员工信息
const selectedCustomer = inject('selectedCustomer', ref(null));

// 处理员工卡片点击事件
const handlecustomerClick = (customer) => {
    // 将选中的员工信息传递给CustomerChatList组件
    if (selectedCustomer) {
        selectedCustomer.value = {
            name: customer.name,
            avatar: customer.avatar,
            label: getCustomerCorpName(customer),
            id: customer.externalUserId || '',
        };
    }
    console.log('选中员工:', customer.name);
};

const handleClick = (tab, event) => {
    console.log(tab, event)
}


// ----------------------------------------------------控制客户类型显示：1-微信 2-企业微信
const handleCustomerType = (type) => {
    switch (type) {
        case 1:
            return "wechat";
        case 2:
            return "ewechat";
        default:
            return "unknown";
    }
};

// ----------------------------------------------------控制客户标签显示：1-@微信 <EMAIL>
const getCustomerCorpName = (customer) => {

    switch (customer.type) {
        case 1:
            if (customer.externalUserId == 'wmykZGPQAApQrCvSTyJRFBdyOQrDpjUg') {
                console.log('触发点7')
            }
            return "@微信";
        case 2:
            return customer.corpName ? `@${customer.corpName}` : '@未知企业';
        default:
            return "";
    }
}



</script>

<style>
.mainContainer-customerlist {
    margin: 0;
    height: calc(100vh - 6.969rem);
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    width: 15.625rem;
    padding: 1rem 0.75rem;
    border-right: 0.0625rem solid #e6e6e6;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.02);
}

.search {
    margin-bottom: 0.3125rem;
}

.search-input {
    transition: all 0.3s ease;
}

.search-input:hover .el-input__wrapper {
    box-shadow: 0 0 0 0.0625rem #e6e6e6 inset;
}

.search-icon {
    color: #909399;
}

.lable-navigator {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.lable-navigator::-webkit-scrollbar {
    width: 0.375rem;
}

.lable-navigator::-webkit-scrollbar-thumb {
    background-color: #e0e0e0;
    border-radius: 0.1875rem;
}

.lable-navigator::-webkit-scrollbar-track {
    background-color: transparent;
}

.customer-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 4rem;
    padding: 0 0.75rem;
    margin: 0.25rem 0;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.customer-card:hover {
    background-color: #f5f7fa;
    transform: translateX(0.125rem);
}

.customer-card .profile {
    /* background: linear-gradient(135deg, #1890ff, #0960bd); */
    /* color: #ffffff; */
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5rem;
    margin-right: 0.75rem;
    /* font-weight: 500; */
    box-shadow: 0 0.125rem 0.25rem rgba(24, 144, 255, 0.1);
}

.customer-card .profile img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.customer-card .info {
    flex: 1;
}

.customer-card .info .name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.customer-card .info .lable.unknown {
    font-size: 0.75rem;
    color: #909399;
}

.customer-card .info .lable.wechat {
    font-size: 0.75rem;
    color: #4bdd1f;
}

.customer-card .info .lable.ewechat {
    font-size: 0.75rem;
    color: #fd6c33;
}

.customer-card .mark {
    margin-left: 0.5rem;
}

.pageContainer {
    display: flex;
    justify-content: center;
    padding: 0.75rem 0;
    margin-top: 0.5rem;
    border-top: 0.0625rem solid #f0f0f0;
    width: 100%;
}

.pageContainer :deep(.el-pagination) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 0 0.5rem;
    font-size: 0.8125rem;
}

.pageContainer :deep(.el-pagination .el-pagination__total) {
    min-width: auto;
    margin-right: 0.5rem;
    font-size: 0.8125rem;
    color: #606266;
}

.pageContainer :deep(.el-pagination .el-pager) {
    margin: 0;
}

.pageContainer :deep(.el-pagination .el-pager li) {
    min-width: 1.5rem;
    height: 1.5rem;
    line-height: 1.5rem;
    font-weight: normal;
    margin: 0 0.125rem;
}

.pageContainer :deep(.el-pagination .el-pager li.active) {
    background-color: #1890ff;
    color: #ffffff;
}

.pageContainer :deep(.el-pagination button) {
    min-width: 1.5rem;
    height: 1.5rem;
    margin: 0;
    padding: 0;
}

.pageContainer :deep(.el-pagination .btn-prev),
.pageContainer :deep(.el-pagination .btn-next) {
    margin: 0 0.125rem;
    background-color: #f4f4f5;
}

.pageContainer :deep(.el-pagination .el-icon) {
    font-size: 0.75rem;
}

.pageContainer :deep(.el-pagination .more) {
    background-color: transparent;
}

.customerList-tabs {
    height: 100%;
}

.customerList-tabs :deep(.el-tabs__header) {
    margin-bottom: 1rem;
}

.customerList-tabs :deep(.el-tabs__nav-wrap::after) {
    height: 0.0625rem;
    background-color: #f0f0f0;
}

.customerList-tabs :deep(.el-tabs__item) {
    font-size: 0.875rem;
    color: #606266;
    padding: 0 1rem;
}

.customerList-tabs :deep(.el-tabs__item.is-active) {
    color: #1890ff;
    font-weight: 500;
}

.customerList-tabs :deep(.el-tabs__active-bar) {
    background-color: #1890ff;
    height: 0.125rem;
}
</style>
