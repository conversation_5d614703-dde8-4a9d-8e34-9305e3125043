{"version": 3, "file": "js/954.90097c6e.js", "mappings": "8LAGUA,MAAM,mB,GAKHA,MAAM,a,gJALfC,EAAAA,EAAAA,IAmBO,OAnBPC,EAmBO,cAlBHC,EAAAA,EAAAA,IAGM,OAHDH,MAAM,cAAY,EACnBG,EAAAA,EAAAA,IAAyB,OAApBH,MAAM,WACXG,EAAAA,EAAAA,IAAyB,OAApBH,MAAM,YAAO,KAEtBG,EAAAA,EAAAA,IAYM,MAZNC,EAYM,cAXFD,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRE,EAAAA,EAAAA,IASUC,EAAA,CATDN,MAAM,OAAQO,MAAOC,EAAAC,KAAOC,UAVjDC,EAAAA,EAAAA,IAUiEC,EAAAC,YAAW,c,CAV5EC,SAAAC,EAAAA,EAAAA,KAWgB,IAEe,EAFfV,EAAAA,EAAAA,IAEeW,EAAA,CAFDhB,MAAM,YAAYiB,MAAM,O,CAXtDH,SAAAC,EAAAA,EAAAA,KAYoB,IAAyE,EAAzEV,EAAAA,EAAAA,IAAyEa,EAAA,CAA/DlB,MAAM,SAZpCmB,WAYuDX,EAAAC,KAAKW,SAZ5D,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAYuDd,EAAAC,KAAKW,SAAQE,GAAEC,YAAY,U,0BAZlFC,EAAA,KAcgBnB,EAAAA,EAAAA,IAGeW,EAAA,CAHDhB,MAAM,YAAYiB,MAAM,M,CAdtDH,SAAAC,EAAAA,EAAAA,KAeoB,IACoB,EADpBV,EAAAA,EAAAA,IACoBa,EAAA,CADVlB,MAAM,SAfpCmB,WAesDX,EAAAC,KAAKgB,SAf3D,sBAAAJ,EAAA,KAAAA,EAAA,GAAAC,GAesDd,EAAAC,KAAKgB,SAAQH,GAAEI,KAAK,WAAWH,YAAY,QACzE,oB,0BAhBxBC,EAAA,KAkBgBnB,EAAAA,EAAAA,IAA+EsB,EAAA,CAApE3B,MAAM,YAAY0B,KAAK,UAAU,cAAY,U,CAlBxEZ,SAAAC,EAAAA,EAAAA,KAkBiF,IAAEM,EAAA,KAAAA,EAAA,KAlBnFO,EAAAA,EAAAA,IAkBiF,UAlBjFJ,EAAA,OAAAA,EAAA,G,4DA+BA,GACIK,KAAM,YAENC,IAAAA,GACI,MAAO,CACHrB,KAAM,CACFW,SAAU,GACVK,SAAU,IAGtB,EAEAM,QAAS,CACLlB,WAAAA,GACSmB,KAAKvB,KAAKW,SAIVY,KAAKvB,KAAKgB,SAIfQ,EAAAA,EAAcC,KAAK,kBAAmBF,KAAKvB,MAAM0B,MAAKC,IAElD,GADAC,QAAQC,IAAIF,GACU,IAAlBA,EAAIN,KAAKS,KAAY,CACrBC,aAAaC,QAAQ,OAAQT,KAAKvB,KAAKW,UACvCoB,aAAaC,QAAQ,eAAgBL,EAAIN,KAAKA,KAAKY,cACnD,MAAMC,EAAiBC,KAAKC,MAA+B,IAAvBT,EAAIN,KAAKA,KAAKgB,OAClDN,aAAaC,QAAQ,aAAcE,GACnCN,QAAQC,IAAI,kCAAmCE,aAAaO,QAAQ,SACpEV,QAAQC,IAAI,sCAAuCE,aAAaO,QAAQ,eACxEV,QAAQC,IAAI,oBAAqBM,KAAKC,OACtCb,KAAKgB,QAAQC,KAAK,aACtB,MACIZ,QAAQC,IAAI,gCAAiCF,EAAIN,MACjDoB,EAAAA,GAAUC,MAAM,iBACpB,IACDC,OAAMD,IACLd,QAAQC,IAAIa,GACZD,EAAAA,GAAUC,MAAM,eAAe,IApB/BD,EAAAA,GAAUC,MAAM,SAJhBD,EAAAA,GAAUC,MAAM,SA0BxB,I,UCjER,MAAME,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,O", "sources": ["webpack://wccsfront/./src/loginPage/login.vue", "webpack://wccsfront/./src/loginPage/login.vue?fdbb"], "sourcesContent": ["<!-- src/views/Login.vue -->\r\n<template>\r\n\r\n    <body class=\"login-container\">\r\n        <div class=\"background\">\r\n            <div class=\"shape\"></div>\r\n            <div class=\"shape\"></div>\r\n        </div>\r\n        <div class=\"login-box\">\r\n            <h2>用户登录</h2>\r\n            <el-form class=\"form\" :model=\"form\" @submit.prevent=\"handleLogin\">\r\n                <el-form-item class=\"form-item\" label=\"用户名\" >\r\n                    <el-input class=\" input1\" v-model=\"form.username\" placeholder=\"请输入用户名\" />\r\n                </el-form-item>\r\n                <el-form-item class=\"form-item\" label=\"密码\">\r\n                    <el-input class=\"input2\" v-model=\"form.password\" type=\"password\" placeholder=\"请输入密码\"\r\n                        show-password />\r\n                </el-form-item>\r\n                <el-button class=\"login-btn\" type=\"primary\" native-type=\"submit\">登录</el-button>\r\n            </el-form>\r\n        </div>\r\n\r\n    </body>\r\n\r\n\r\n</template>\r\n\r\n<script>\r\nimport { ElMessage } from 'element-plus'\r\nimport axiosInstance from '../axiosConfig.js'; // 引入 axios 实例\r\n\r\nexport default {\r\n    name: 'UserLogin',\r\n\r\n    data() {\r\n        return {\r\n            form: {\r\n                username: '',\r\n                password: ''\r\n            }\r\n        };\r\n    },\r\n\r\n    methods: {\r\n        handleLogin() {\r\n            if (!this.form.username) {\r\n                ElMessage.error('请输入用户名');\r\n                return;\r\n            }\r\n            if (!this.form.password) {\r\n                ElMessage.error('请输入密码');\r\n                return;\r\n            }\r\n            axiosInstance.post('/api/user/login', this.form).then(res => {\r\n                console.log(res);\r\n                if (res.data.code === 0) {\r\n                    localStorage.setItem('user', this.form.username);\r\n                    localStorage.setItem('access_token', res.data.data.access_token);\r\n                    const expirationTime = Date.now() + res.data.data.expire * 1000; // 计算失效时间点（毫秒）\r\n                    localStorage.setItem('expireTime', expirationTime);\r\n                    console.log(\"log2——登录测试，localStroage中存储的用户名：\", localStorage.getItem('user'));\r\n                    console.log(\"log4——登录测试，localStroage中存储的下次失效时间点：\", localStorage.getItem('expireTime'));\r\n                    console.log(\"log6——登录测试，当前时间点：\", Date.now());\r\n                    this.$router.push('/wxArchive');\r\n                } else {\r\n                    console.log(\"log5——登录测试，请求成功但登录失败（用户密码错误）：\", res.data);\r\n                    ElMessage.error('登录失败，请检查用户名和密码');\r\n                }\r\n            }).catch(error => {\r\n                console.log(error);\r\n                ElMessage.error('登录请求出错，请稍后重试');//可能的情况：1. 网络问题；2. 后端服务器未启动\r\n            });\r\n        }\r\n    }\r\n};\r\n</script>\r\n\r\n\r\n<style>\r\n*， *::before,\r\n*::after {\r\n    padding: 0;\r\n    margin: 0;\r\n    box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n    background-color: #569ff7;\r\n}\r\n\r\n.background {\r\n\r\n    width: 300px;\r\n    height: 200px;\r\n    /* background-color: aliceblue; */\r\n\r\n    /* 实现绝对居中的方法： */\r\n    position: absolute;\r\n    transform: translate(-50%, -50%);\r\n    left: 50%;\r\n    top: 50%;\r\n}\r\n\r\n.background .shape {\r\n    position: absolute;\r\n    height: 150px;\r\n    width: 150px;\r\n    border-radius: 50%;\r\n}\r\n\r\n.background .shape:nth-child(1) {\r\n    background: linear-gradient(#0F71E6, #23a2f6);\r\n    left: -80px;\r\n    top: -110px;\r\n}\r\n\r\n.background .shape:nth-child(2) {\r\n    right: -80px;\r\n    bottom: -80px;\r\n    background: linear-gradient(to right, #f6d365, #fda085);\r\n}\r\n\r\n.login-box * {\r\n    font-family: 'Microsoft YaHei', sans-serif;\r\n    /* color: #ffff; */\r\n    letter-spacing: 0.5px;\r\n    outline: none;\r\n    border: none;\r\n\r\n}\r\n\r\n.login-box {\r\n    height: 200px;\r\n    width: 280px;\r\n    background-color: rgba(255, 255, 255, 0.13);\r\n    position: absolute;\r\n    transform: translate(-50%, -50%);\r\n    left: 50%;\r\n    top: 50%;\r\n\r\n    border-radius: 10px;\r\n    backdrop-filter: blur(10px);\r\n    border: 2px solid rgba(255, 255, 255, 0.1);\r\n    box-shadow: 0 0 40px rgba(8, 7, 16, 0.6);\r\n    padding: 50px 35px;\r\n}\r\n\r\n.login-box h2 {\r\n    font-size: 20px;\r\n    text-align: center;\r\n    font-weight: 500;\r\n    line-height: 42px;\r\n    color: white;\r\n}\r\n\r\n.login-box .form {\r\n    margin-top: 30px;\r\n}\r\n\r\n.el-form-item__label {\r\n    color: white;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n.login-box .login-btn {\r\n    position: absolute;\r\n    transform: translate(-50%, -50%);\r\n    left: 50%;\r\n    top: 80%;\r\n    margin-top: 10px;\r\n    width: 100px;\r\n}\r\n\r\n.login-box .input2 {\r\n    margin-left: 13px;\r\n    color: black;\r\n}\r\n\r\n\r\n\r\n.el-input {\r\n    /*获取焦点后的边框颜色*/\r\n    --el-input-focus-border-color: rgb(0, 140, 255);\r\n    /*鼠标悬停边框颜色*/\r\n    --el-input-hover-border-color: #00c3ff;\r\n    --el-input-text-color: black;\r\n    transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.el-input:hover {\r\n    box-shadow: 0 0 10px rgba(188, 239, 255, 0.959);\r\n    /* 调整阴影参数，比如模糊、颜色透明度等 */\r\n}\r\n</style>\r\n", "import { render } from \"./login.vue?vue&type=template&id=a00cdbd4\"\nimport script from \"./login.vue?vue&type=script&lang=js\"\nexport * from \"./login.vue?vue&type=script&lang=js\"\n\nimport \"./login.vue?vue&type=style&index=0&id=a00cdbd4&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_form", "model", "$data", "form", "onSubmit", "_withModifiers", "$options", "handleLogin", "default", "_withCtx", "_component_el_form_item", "label", "_component_el_input", "modelValue", "username", "_cache", "$event", "placeholder", "_", "password", "type", "_component_el_button", "_createTextVNode", "name", "data", "methods", "this", "axiosInstance", "post", "then", "res", "console", "log", "code", "localStorage", "setItem", "access_token", "expirationTime", "Date", "now", "expire", "getItem", "$router", "push", "ElMessage", "error", "catch", "__exports__", "render"], "sourceRoot": ""}