"use strict";(self["webpackChunkwccsfront"]=self["webpackChunkwccsfront"]||[]).push([[843],{1565:function(e,a,l){e.exports=l.p+"img/群聊头像.b7b14578.png"},5985:function(e){e.exports="data:image/png;base64,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*******************************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"},6843:function(e,a,l){l.r(a),l.d(a,{default:function(){return R}});var t=l(6768),n=l(4232),s=l(144),c=l(5985),o=l(1565),u=l(7477),i=l(1314),r=l(1219);const A={class:"mainContainer-employeechatlist"},v={class:"selected-employee-card"},p={class:"employee-info"},g={class:"name"},m={class:"search",style:{width:"600px"}},d={class:"lable-navigator"},k=["onClick"],f={class:"info"},C={class:"name"},h=["onClick"],E={class:"profile"},w=["src"],B={class:"info"},b={class:"name"},S=["onClick"],x={class:"info"},J={class:"name"},U={class:"pageContainer"};var W={__name:"EmployeeChatList",setup(e){const a=(0,s.KR)(""),W=(0,s.KR)("first"),K=(0,s.KR)([]),R=(0,s.KR)(""),L=(0,s.KR)(1),F=(0,s.KR)(20),G=(0,s.KR)(!1),I=(0,t.WQ)("selectedEmployee",null),M=(0,s.KR)("未选择员工"),T=(0,t.WQ)("selectedChat",null);(0,t.wB)(I,(e=>{e&&(console.log("EmployeeChatList：监听到选中员工"),M.value=e.name,L.value=1,W.value="first",a.value="",X(I.value,L.value,F.value,"employee",a.value))}));const X=(e,a,l,t,n)=>{const s=localStorage.getItem("access_token");i.A.post("/api/chatmessage/conversations",{from:"employee",to:t,id:e.id,page:a,limit:l,name:n,hasConversation:G.value},{headers:{Authorization:"Bearer "+s}}).then((e=>{console.log(e.data.length),K.value=e.data.data.data,R.value=e.data.data.total})).catch((e=>{console.log(e),r.nk.error("获取选中人员会话列表失败，请检查网络或联系管理员")}))},Z=e=>{L.value=e;const l=new Map([["first","employee"],["second","customer"],["third","groupchat"]]),t=l.get(W.value);X(I.value,L.value,F.value,t,a.value)},D=()=>{if(!I.value)return void r.nk.warning("请先在左侧面板选择要查看的人员");const e=new Map([["first","employee"],["second","customer"],["third","groupchat"]]),l=e.get(W.value);a.value&&(L.value=1),X(I.value,L.value,F.value,l,a.value)};(0,t.wB)(a,D),(0,t.wB)(G,D);const Y=()=>{const e=new Map([["first","employee"],["second","customer"],["third","groupchat"]]),l=e.get(W.value);I.value?X(I.value,L.value,F.value,l,a.value):r.nk.warning("请先在左侧面板选择要查看的人员")},Q=(e,a)=>{let t="",n="",s="",c="",o="",u="",i="",r="",A="",v=1,p=30;"employee"==a?(t=I.value.id,n=e.userid,s=I.value.name,c="@员工",o=l(5985),u=e.name,i="",r="@员工",A=""):"customer"==a?(t=I.value.id,n=e.externalUserId,s=I.value.name,c="@员工",o=l(5985),u=e.name,i=e.avatar,r=y(e),A=""):"groupchat"==a&&(t=e.chatId,n="",s=V(e),c="",o="",u="",i="",r="",A="groupchat");const g={from:t,to:n,fromName:s,fromLable:c,fromAvatar:o,toName:u,toAvatar:i,toLable:r,type:A,page:v,limit:p};T.value=g,console.log("检测到某一会话被点击",g)},y=e=>{switch(e.type){case 1:return"@微信";case 2:return e.corpName?`@${e.corpName}`:"@未知企业";default:return""}},V=e=>{if(e.name)return e.name;const a=e.chatId,l="群聊";return a&&a.length>6?(e.name=l+a.slice(0,9)+"...",l+a.slice(0,9)+"..."):""};return(e,l)=>{const i=(0,t.g2)("el-icon"),r=(0,t.g2)("el-input"),I=(0,t.g2)("el-switch"),T=(0,t.g2)("el-tab-pane"),X=(0,t.g2)("el-tabs"),D=(0,t.g2)("el-pagination");return(0,t.uX)(),(0,t.CE)("div",A,[(0,t.Lk)("div",v,[l[4]||(l[4]=(0,t.Lk)("div",{class:"profile-box"},[(0,t.Lk)("div",{class:"avatar"},[(0,t.Lk)("img",{src:c,alt:"人员头像"})])],-1)),(0,t.Lk)("div",p,[(0,t.Lk)("p",g,(0,n.v_)(M.value),1)])]),(0,t.Lk)("div",m,[(0,t.bF)(r,{placeholder:"搜索",modelValue:a.value,"onUpdate:modelValue":l[0]||(l[0]=e=>a.value=e),clearable:"",class:"search-input",style:{width:"200px"}},{prefix:(0,t.k6)((()=>[(0,t.bF)(i,{class:"search-icon"},{default:(0,t.k6)((()=>[(0,t.bF)((0,s.R1)(u.Search))])),_:1})])),_:1},8,["modelValue"]),(0,t.bF)(I,{modelValue:G.value,"onUpdate:modelValue":l[1]||(l[1]=e=>G.value=e),"inline-prompt":"","active-text":"已沟通","inactive-text":"全部"},null,8,["modelValue"])]),(0,t.Lk)("div",d,[(0,t.bF)(X,{modelValue:W.value,"onUpdate:modelValue":l[2]||(l[2]=e=>W.value=e),class:"employeechatList-tabs",onTabClick:Y},{default:(0,t.k6)((()=>[(0,t.bF)(T,{label:"员工",name:"first"},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(K.value,(e=>((0,t.uX)(),(0,t.CE)("div",{class:"chat-staff-card",key:e.name,onClick:a=>Q(e,"employee")},[l[6]||(l[6]=(0,t.Lk)("div",{class:"profile"},[(0,t.Lk)("img",{class:"staff-avatar",src:c,alt:"人员头像"})],-1)),(0,t.Lk)("div",f,[(0,t.Lk)("p",C,(0,n.v_)(e.name),1),l[5]||(l[5]=(0,t.Lk)("p",{class:"latest-message"},null,-1))])],8,k)))),128))])),_:1}),(0,t.bF)(T,{label:"客户",name:"second"},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(K.value,(e=>((0,t.uX)(),(0,t.CE)("div",{class:"chat-staff-card",key:e.name,onClick:a=>Q(e,"customer")},[(0,t.Lk)("div",E,[(0,t.Lk)("img",{class:"staff-avatar",src:e.avatar,alt:"用户头像"},null,8,w)]),(0,t.Lk)("div",B,[(0,t.Lk)("p",b,(0,n.v_)(e.name),1),l[7]||(l[7]=(0,t.Lk)("p",{class:"latest-message"},null,-1))])],8,h)))),128))])),_:1}),(0,t.bF)(T,{label:"群聊",name:"third"},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(K.value,((e,a)=>((0,t.uX)(),(0,t.CE)("div",{class:"chat-staff-card",key:a,onClick:a=>Q(e,"groupchat")},[l[9]||(l[9]=(0,t.Lk)("div",{class:"profile group-avatar"},[(0,t.Lk)("img",{src:o,alt:"群聊头像"})],-1)),(0,t.Lk)("div",x,[(0,t.Lk)("p",J,(0,n.v_)(V(e)),1),l[8]||(l[8]=(0,t.Lk)("p",{class:"latest-message"},null,-1))])],8,S)))),128))])),_:1})])),_:1},8,["modelValue"])]),(0,t.Lk)("div",U,[(0,t.bF)(D,{"current-page":L.value,"onUpdate:currentPage":l[3]||(l[3]=e=>L.value=e),background:"",size:e.size,layout:"total, prev, pager, next",total:R.value,"page-size":F.value,"pager-count":3,small:"",onCurrentChange:Z},null,8,["current-page","size","total","page-size"])])])}}};const K=W;var R=K}}]);
//# sourceMappingURL=843.75dbb687.js.map