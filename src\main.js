import { createApp } from 'vue'
import App from './App.vue'

//引入element-ui
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIcons from '@element-plus/icons-vue'

//引入路由
import VueRouter from './router'

//引入reset.css
import 'reset-css/reset.css'

//引入vue-audio-visual
import { AVPlugin } from 'vue-audio-visual'



const app = createApp(App)
app.use(ElementPlus)
app.use(VueRouter)
app.use(ElementPlusIcons)
app.use(AVPlugin)

app.mount('#app')

