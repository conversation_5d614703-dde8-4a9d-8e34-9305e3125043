{"version": 3, "file": "js/843.d13b467c.js", "mappings": "wjVAsFA,MAAMA,GAAaC,EAAAA,EAAAA,IAAI,IACjBC,GAAaD,EAAAA,EAAAA,IAAI,SAEjBE,GAAYF,EAAAA,EAAAA,IAAI,IAChBG,GAAaH,EAAAA,EAAAA,IAAI,IACjBI,GAAcJ,EAAAA,EAAAA,IAAI,GAClBK,GAAWL,EAAAA,EAAAA,IAAI,IAEfM,GAAkBN,EAAAA,EAAAA,KAAI,GAKtBO,GAAmBC,EAAAA,EAAAA,IAAO,mBAAoB,MAC9CC,GAAuBT,EAAAA,EAAAA,IAAI,SAI3BU,GAAeF,EAAAA,EAAAA,IAAO,eAAgB,OAG5CG,EAAAA,EAAAA,IAAMJ,GAAmBK,IACjBA,IACAC,QAAQC,IAAI,4BACZL,EAAqBM,MAAQH,EAASI,KAItCZ,EAAYW,MAAQ,EAEpBd,EAAWc,MAAQ,QAEnBhB,EAAWgB,MAAQ,GAGnBE,EAAYV,EAAiBQ,MAAOX,EAAYW,MAAOV,EAASU,MAAO,WAAYhB,EAAWgB,OAClG,IAMJ,MAAME,EAAcA,CAACV,EAAkBH,EAAaC,EAAUa,EAAenB,KACzE,MAAMoB,EAAYC,aAAaC,QAAQ,gBACvCC,EAAAA,EAAcC,KAAK,iCAAkC,CACjDC,KAAM,WACNC,GAAIP,EACJQ,GAAInB,EAAiBmB,GACrBC,KAAMvB,EACNwB,MAAOvB,EACPW,KAAMjB,EACN8B,gBAAiBvB,EAAgBS,OAClC,CACCe,QAAS,CAAEC,cAAe,UAAYZ,KACvCa,MAAKC,IACJpB,QAAQC,IAAImB,EAAIC,KAAKC,QACrBjC,EAAUa,MAAQkB,EAAIC,KAAKA,KAAKA,KAEhC/B,EAAWY,MAAQkB,EAAIC,KAAKA,KAAKE,KAAK,IACvCC,OAAMC,IACLzB,QAAQC,IAAIwB,GACZC,EAAAA,GAAUD,MAAM,2BAA2B,GAC7C,EAuBAE,EAAoBb,IACtBvB,EAAYW,MAAQY,EAGpB,MAAMc,EAAc,IAAIC,IAAI,CACxB,CAAC,QAAS,YACV,CAAC,SAAU,YACX,CAAC,QAAS,eAERC,EAAWF,EAAYG,IAAI3C,EAAWc,OAK5CE,EAAYV,EAAiBQ,MAAOX,EAAYW,MAAOV,EAASU,MAAO4B,EAAU5C,EAAWgB,MAAM,EAKhG8B,EAAiBA,KACnB,IAAKtC,EAAiBQ,MAElB,YADAwB,EAAAA,GAAUO,QAAQ,mBAMtB,MAAML,EAAc,IAAIC,IAAI,CACxB,CAAC,QAAS,YACV,CAAC,SAAU,YACX,CAAC,QAAS,eAERC,EAAWF,EAAYG,IAAI3C,EAAWc,OAExChB,EAAWgB,QACXX,EAAYW,MAAQ,GAExBE,EAAYV,EAAiBQ,MAAOX,EAAYW,MAAOV,EAASU,MAAO4B,EAAU5C,EAAWgB,MAAM,GAStGJ,EAAAA,EAAAA,IAAMZ,EAAY8C,IAElBlC,EAAAA,EAAAA,IAAML,EAAiBuC,GAIvB,MAAME,EAAcA,KAIhB,MAAMN,EAAc,IAAIC,IAAI,CACxB,CAAC,QAAS,YACV,CAAC,SAAU,YACX,CAAC,QAAS,eAERC,EAAWF,EAAYG,IAAI3C,EAAWc,OAEvCR,EAAiBQ,MAItBE,EAAYV,EAAiBQ,MAAOX,EAAYW,MAAOV,EAASU,MAAO4B,EAAU5C,EAAWgB,OAHxFwB,EAAAA,GAAUO,QAAQ,kBAG4E,EAKhGE,EAAkBA,CAACC,EAAOC,KAC5B,IAAI1B,EAAO,GACPC,EAAK,GAEL0B,EAAW,GACXC,EAAY,GACZC,EAAa,GAEbC,EAAS,GACTC,EAAW,GACXC,EAAU,GAEVC,EAAO,GACP9B,EAAO,EACPC,EAAQ,GAEI,YAAZsB,GACA1B,EAAOjB,EAAiBQ,MAAMW,GAC9BD,EAAKwB,EAAMS,OAEXP,EAAW5C,EAAiBQ,MAAMC,KAClCoC,EAAY,MACZC,EAAaM,EAAQ,MAErBL,EAASL,EAAMjC,KACfuC,EAAW,GACXC,EAAU,MAEVC,EAAO,IAEU,YAAZP,GACL1B,EAAOjB,EAAiBQ,MAAMW,GAC9BD,EAAKwB,EAAMW,eAEXT,EAAW5C,EAAiBQ,MAAMC,KAClCoC,EAAY,MACZC,EAAaM,EAAQ,MAErBL,EAASL,EAAMjC,KACfuC,EAAWN,EAAMY,OACjBL,EAAUM,EAAiBb,GAC3BQ,EAAO,IAEU,aAAZP,IACL1B,EAAOyB,EAAMc,OACbtC,EAAK,GAEL0B,EAAWa,EAAaf,GACxBG,EAAY,GACZC,EAAa,GACbC,EAAS,GACTC,EAAW,GACXC,EAAU,GAEVC,EAAO,aAGX,MAAMQ,EAAoB,CACtBzC,KAAMA,EACNC,GAAIA,EAEJ0B,SAAUA,EACVC,UAAWA,EACXC,WAAYA,EAEZC,OAAQA,EACRC,SAAUA,EACVC,QAASA,EAETC,KAAMA,EACN9B,KAAMA,EACNC,MAAOA,GAEXlB,EAAaK,MAAQkD,EAErBpD,QAAQC,IAAI,aAAcmD,EAAkB,EAI1CH,EAAoBI,IACtB,OAAQA,EAAST,MACb,KAAK,EACD,MAAO,MACX,KAAK,EACD,OAAOS,EAASC,SAAW,IAAID,EAASC,WAAa,QACzD,QACI,MAAO,GACf,EAKEH,EAAgBI,IAClB,GAAIA,EAAMpD,KACN,OAAOoD,EAAMpD,KAEjB,MAAMqD,EAAUD,EAAML,OAChBO,EAAY,KAClB,OAAID,GAAWA,EAAQlC,OAAS,GAC5BiC,EAAMpD,KAAOsD,EAAYD,EAAQE,MAAM,EAAG,GAAK,MACxCD,EAAYD,EAAQE,MAAM,EAAG,GAAK,OAEtC,EAAE,E,upFCpVb,MAAMC,EAAc,EAEpB,O", "sources": ["webpack://wccsfront/./src/components/ChatArchive/EmployeeChat/EmployeeChatList.vue", "webpack://wccsfront/./src/components/ChatArchive/EmployeeChat/EmployeeChatList.vue?4930"], "sourcesContent": ["<!-- TODO：\r\n固定搜索框的宽度 -->\r\n<template>\r\n    <div class=\"mainContainer-employeechatlist\">\r\n        <div class=\"selected-employee-card\">\r\n            <div class=\"profile-box\">\r\n                <div class=\"avatar\">\r\n                    <img src=\"../../../assets/人员头像.png\" alt=\"人员头像\">\r\n                </div>\r\n            </div>\r\n            <div class=\"employee-info\">\r\n                <p class=\"name\">{{ selectedEmployeeName }}</p>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"search\" style=\"width: 600px;\">\r\n            <el-input placeholder=\"搜索\" v-model=\"searchWord\" clearable class=\"search-input\" style=\"width: 200px;\">\r\n                <template #prefix>\r\n                    <el-icon class=\"search-icon\">\r\n                        <Search />\r\n                    </el-icon>\r\n                </template>\r\n            </el-input>\r\n\r\n            <el-switch v-model=\"hasCommunicated\" inline-prompt active-text=\"已沟通\" inactive-text=\"全部\" />\r\n        </div>\r\n\r\n        <!-- @click=\"getDetailChat(\"employee\",\"employee\")\" -->\r\n        <div class=\"lable-navigator\">\r\n            <el-tabs v-model=\"activeName\" class=\"employeechatList-tabs\" @tab-click=\"handleClick\">\r\n                <el-tab-pane label=\"员工\" name=\"first\">\r\n                    <div class=\"chat-staff-card\" v-for=\"staff in staffList\" :key=\"staff.name\"\r\n                        @click=\"handleChatClick(staff, 'employee')\">\r\n                        <div class=\"profile\">\r\n                            <img class=\"staff-avatar\" src=\"@/assets/人员头像.png\" alt=\"人员头像\">\r\n                        </div>\r\n                        <div class=\"info\">\r\n                            <p class=\"name\">{{ staff.name }}</p>\r\n                            <p class=\"latest-message\"></p>\r\n                        </div>\r\n\r\n                    </div>\r\n                </el-tab-pane>\r\n\r\n                <el-tab-pane label=\"客户\" name=\"second\">\r\n                    <div class=\"chat-staff-card\" v-for=\"customer in staffList\" :key=\"customer.name\"\r\n                        @click=\"handleChatClick(customer, 'customer')\">\r\n                        <div class=\"profile\">\r\n                            <img class=\"staff-avatar\" :src=\"customer.avatar\" alt=\"用户头像\">\r\n                        </div>\r\n                        <div class=\"info\">\r\n                            <p class=\"name\">{{ customer.name }}</p>\r\n                            <p class=\"latest-message\"></p>\r\n                        </div>\r\n                    </div>\r\n                </el-tab-pane>\r\n\r\n                <el-tab-pane label=\"群聊\" name=\"third\">\r\n                    <div class=\"chat-staff-card\" v-for=\"(group, index) in staffList\" :key=\"index\"\r\n                        @click=\"handleChatClick(group, 'groupchat')\">\r\n                        <div class=\"profile group-avatar\">\r\n                            <img src=\"../../../assets/群聊头像.png\" alt=\"群聊头像\">\r\n                        </div>\r\n                        <div class=\"info\">\r\n                            <p class=\"name\">{{ getGroupName(group) }}</p>\r\n                            <p class=\"latest-message\"></p>\r\n                        </div>\r\n                    </div>\r\n                </el-tab-pane>\r\n            </el-tabs>\r\n        </div>\r\n\r\n        <div class=\"pageContainer\">\r\n            <el-pagination v-model:current-page=\"currentPage\" background :size=\"size\" layout=\"total, prev, pager, next\"\r\n                :total=\"totalStaff\" :page-size=\"pageSize\" :pager-count=\"3\" small @current-change=\"handlePageChange\">\r\n            </el-pagination>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { Search } from '@element-plus/icons-vue';\r\nimport { ref, watch, inject } from 'vue';\r\nimport axiosInstance from '@/axiosConfig.js';\r\nimport { ElMessage } from 'element-plus';\r\n\r\nconst searchWord = ref(''); // searchWord 响应搜索框\r\nconst activeName = ref('first') // activeName 响应 tab 切换，以及分页查询时的会话类型传参\r\n\r\nconst staffList = ref([]); // 会话列表\r\nconst totalStaff = ref(''); // totalEmployees 分页组件展示的人员总数\r\nconst currentPage = ref(1); // currentPage 当前页码\r\nconst pageSize = ref(20); // pageSize 每页显示数量\r\n\r\nconst hasCommunicated = ref(false); // 控制是否只显示已沟通的会话\r\n\r\n\r\n\r\n// 注入selectedEmployee，如果没有提供则默认为null\r\nconst selectedEmployee = inject('selectedEmployee', null);\r\nconst selectedEmployeeName = ref('未选择员工'); // selectedEmployeeName 选中员工姓名\r\n// const selectedEmployeeAvatar = ref(require('@/assets/人员头像.png'))\r\n\r\n//注入选中的会话\r\nconst selectedChat = inject('selectedChat', null);\r\n\r\n// 监听选中员工的变化，当有员工被选中时执行相应操作\r\nwatch(selectedEmployee, (newValue) => {\r\n    if (newValue) {\r\n        console.log('EmployeeChatList：监听到选中员工');\r\n        selectedEmployeeName.value = newValue.name;\r\n        // selectedEmployeeAvatar.value = newValue.avatar;\r\n\r\n        //重置当前页数\r\n        currentPage.value = 1;\r\n        //重置当前激活标签\r\n        activeName.value = 'first'\r\n        //重置搜索词\r\n        searchWord.value = ''\r\n\r\n        //获取选中人员的会话列表\r\n        getChatList(selectedEmployee.value, currentPage.value, pageSize.value, 'employee', searchWord.value);\r\n    }\r\n});\r\n\r\n\r\n\r\n// 获取存档人员列表\r\nconst getChatList = (selectedEmployee, currentPage, pageSize, staffChatType, searchWord) => {\r\n    const jwt_token = localStorage.getItem('access_token')\r\n    axiosInstance.post('/api/chatmessage/conversations', {\r\n        from: 'employee',\r\n        to: staffChatType,\r\n        id: selectedEmployee.id,\r\n        page: currentPage,\r\n        limit: pageSize,\r\n        name: searchWord,\r\n        hasConversation: hasCommunicated.value\r\n    }, {\r\n        headers: { Authorization: 'Bearer ' + jwt_token }\r\n    }).then(res => {\r\n        console.log(res.data.length);\r\n        staffList.value = res.data.data.data;\r\n        //数量\r\n        totalStaff.value = res.data.data.total;\r\n    }).catch(error => {\r\n        console.log(error);\r\n        ElMessage.error('获取选中人员会话列表失败，请检查网络或联系管理员');\r\n    });\r\n}\r\n\r\n//TODO：时间格式化\r\n// const formatTime = (time) => {\r\n//     const now = new Date();\r\n//     const messageDate = new Date(time);\r\n//     const messageYear = messageDate.getFullYear();\r\n//     const currentYear = now.getFullYear();\r\n//     if (messageYear === currentYear) {\r\n//         if (messageDate.getMonth() === now.getMonth() && messageDate.getDate() === now.getDate()) {\r\n//             // return messageDate.getHours() + ':' + messageDate.getMinutes();\r\n//             return `${String(messageDate.getHours().padStart(2, '0'))}:${String(messageDate.getMinutes().padStart(2, '0'))}`;\r\n//         } else {\r\n//             return `${String(messageDate.getMonth()).padStart(2, '0')}-${String(messageDate.getDate()).padStart(2, '0')}`;\r\n//         }\r\n//     } else {\r\n//         return `${String(messageDate.getFullYear())}-${String(messageDate.getMonth()).padStart(2, '0')}-${String(messageDate.getDate()).padStart(2, '0')}`;\r\n//     }\r\n// }\r\n\r\n\r\n//页码变化时调用\r\nconst handlePageChange = (page) => {\r\n    currentPage.value = page;//当前页码\r\n    //通过activeName来判断会话类型传参\r\n    //创建一个map集合\r\n    const chatTypeMap = new Map([\r\n        ['first', 'employee'],\r\n        ['second', 'customer'],\r\n        ['third', 'groupchat']\r\n    ]);\r\n    const chatType = chatTypeMap.get(activeName.value);\r\n\r\n    //重置搜索词\r\n    // searchWord.value = ''\r\n\r\n    getChatList(selectedEmployee.value, currentPage.value, pageSize.value, chatType, searchWord.value);\r\n}\r\n\r\n\r\n// 搜索框变化时调用\r\nconst filterChatList = () => {\r\n    if (!selectedEmployee.value) {\r\n        ElMessage.warning('请先在左侧面板选择要查看的人员');\r\n        return;\r\n    }\r\n\r\n    //通过activeName来判断会话类型传参\r\n    //创建一个map集合\r\n    const chatTypeMap = new Map([\r\n        ['first', 'employee'],\r\n        ['second', 'customer'],\r\n        ['third', 'groupchat']\r\n    ]);\r\n    const chatType = chatTypeMap.get(activeName.value);\r\n\r\n    if (searchWord.value) {\r\n        currentPage.value = 1; // 重置为第一页\r\n    }\r\n    getChatList(selectedEmployee.value, currentPage.value, pageSize.value, chatType, searchWord.value);\r\n    // const filteredGroups = staffList.value.filter(group =>\r\n    //     group.name.toLowerCase().includes(searchWord.value.toLowerCase())\r\n    // );\r\n    // staffList.value = filteredGroups;\r\n    // totalStaff.value = filteredGroups.length;\r\n}\r\n\r\n\r\nwatch(searchWord, filterChatList);\r\n\r\nwatch(hasCommunicated, filterChatList);\r\n\r\n\r\n//会话类型标签被点击时\r\nconst handleClick = () => {\r\n    //通过activeName来判断会话类型传参\r\n    //创建一个map集合\r\n\r\n    const chatTypeMap = new Map([\r\n        ['first', 'employee'],\r\n        ['second', 'customer'],\r\n        ['third', 'groupchat']\r\n    ]);\r\n    const chatType = chatTypeMap.get(activeName.value);\r\n\r\n    if (!selectedEmployee.value) {\r\n        ElMessage.warning('请先在左侧面板选择要查看的人员');\r\n        return;\r\n    }\r\n    getChatList(selectedEmployee.value, currentPage.value, pageSize.value, chatType, searchWord.value);\r\n}\r\n\r\n\r\n//某一会话被点击时\r\nconst handleChatClick = (staff, chattype) => {\r\n    let from = '' //会话发起方id，如果 type = groupchat，为群聊ID；\r\n    let to = '' //会话接收方id，如果 type = groupchat，为 null；默认为员工id\r\n\r\n    let fromName = '' //会话发起方名称，如果 type = groupchat，为群聊名称；\r\n    let fromLable = '' //会话发起方标签，如果 type = groupchat，为null；\r\n    let fromAvatar = ''\r\n\r\n    let toName = '' //会话接收方名称，如果 type = groupchat，为 null；\r\n    let toAvatar = '' //会话接收方头像 ，如果 type = groupchat，为 null；\r\n    let toLable = '' //会话接收方类型 ，如果 type = groupchat，为 null,为employee时是员工的标签，默认是‘员工’；\r\n\r\n    let type = '' //会话接收方类型，群聊 type = groupchat，\r\n    let page = 1\r\n    let limit = 30\r\n\r\n    if (chattype == 'employee') {\r\n        from = selectedEmployee.value.id\r\n        to = staff.userid\r\n\r\n        fromName = selectedEmployee.value.name\r\n        fromLable = '@员工'\r\n        fromAvatar = require('../../../assets/人员头像.png')\r\n\r\n        toName = staff.name\r\n        toAvatar = ''\r\n        toLable = '@员工'\r\n\r\n        type = ''\r\n    }\r\n    else if (chattype == 'customer') {\r\n        from = selectedEmployee.value.id\r\n        to = staff.externalUserId\r\n\r\n        fromName = selectedEmployee.value.name\r\n        fromLable = '@员工'\r\n        fromAvatar = require('../../../assets/人员头像.png')\r\n\r\n        toName = staff.name\r\n        toAvatar = staff.avatar\r\n        toLable = getCustomerLable(staff)\r\n        type = ''\r\n    }\r\n    else if (chattype == 'groupchat') {\r\n        from = staff.chatId\r\n        to = ''\r\n\r\n        fromName = getGroupName(staff)\r\n        fromLable = ''\r\n        fromAvatar = ''\r\n        toName = ''\r\n        toAvatar = ''\r\n        toLable = ''\r\n\r\n        type = 'groupchat'\r\n    }\r\n\r\n    const selectedChat_info = {\r\n        from: from,\r\n        to: to,\r\n\r\n        fromName: fromName,\r\n        fromLable: fromLable,\r\n        fromAvatar: fromAvatar,\r\n\r\n        toName: toName,\r\n        toAvatar: toAvatar,\r\n        toLable: toLable,\r\n\r\n        type: type,//groupchat（群聊），其它一律视为非群聊\r\n        page: page,\r\n        limit: limit\r\n    }\r\n    selectedChat.value = selectedChat_info\r\n\r\n    console.log('检测到某一会话被点击', selectedChat_info)\r\n\r\n}\r\n\r\nconst getCustomerLable = (customer) => {\r\n    switch (customer.type) {\r\n        case 1:\r\n            return \"@微信\";\r\n        case 2:\r\n            return customer.corpName ? `@${customer.corpName}` : '@未知企业';\r\n        default:\r\n            return \"\";\r\n    }\r\n\r\n}\r\n\r\n// --------------------------------------------------------------------------控制群名显示\r\nconst getGroupName = (group) => {\r\n    if (group.name) {\r\n        return group.name\r\n    }\r\n    const groupId = group.chatId\r\n    const groupName = '群聊'\r\n    if (groupId && groupId.length > 6) {\r\n        group.name = groupName + groupId.slice(0, 9) + '...'\r\n        return groupName + groupId.slice(0, 9) + '...'\r\n    }\r\n    return ''\r\n}\r\n\r\n</script>\r\n\r\n<style>\r\n.mainContainer-employeechatlist {\r\n    margin: 0;\r\n    height: calc(100vh - 6.969rem);\r\n    background-color: #ffffff;\r\n    display: flex;\r\n    flex-direction: column;\r\n    width: 17.5rem;\r\n    padding: 1rem 0.75rem;\r\n    border-right: 0.0625rem solid #e6e6e6;\r\n    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.02);\r\n}\r\n\r\n.mainContainer-employeechatlist .search {\r\n    display: flex;\r\n    flex-direction: row;\r\n\r\n}\r\n\r\n.mainContainer-employeechatlist .el-input {\r\n    display: flex;\r\n    flex-direction: row;\r\n    margin-right: 1.1rem;\r\n}\r\n\r\n\r\n.selected-employee-card {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    padding: 0.75rem;\r\n    margin-bottom: 1rem;\r\n    background-color: #f5f7fa;\r\n    border-radius: 0.5rem;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.selected-employee-card .profile-box {\r\n    margin-right: 0.75rem;\r\n}\r\n\r\n.selected-employee-card .avatar {\r\n    /* background: linear-gradient(135deg, #1890ff, #0960bd); */\r\n    background-color: #ffffff;\r\n    width: 2.5rem;\r\n    height: 2.5rem;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    border-radius: 0.5rem;\r\n    font-weight: 500;\r\n    /* box-shadow: 0 0.125rem 0.25rem rgba(24, 144, 255, 0.1); */\r\n}\r\n\r\n.selected-employee-card .employee-info {\r\n    flex: 1;\r\n}\r\n\r\n.selected-employee-card .employee-info .name {\r\n    font-size: 0.9375rem;\r\n    font-weight: 500;\r\n    color: #2c3e50;\r\n    margin-bottom: 0.25rem;\r\n}\r\n\r\n.selected-employee-card .employee-info .status {\r\n    font-size: 0.75rem;\r\n    color: #67c23a;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n.lable-navigator {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar {\r\n    width: 0.375rem;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar-thumb {\r\n    background-color: #e0e0e0;\r\n    border-radius: 0.1875rem;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar-track {\r\n    background-color: transparent;\r\n}\r\n\r\n.el-tabs--card {\r\n    height: 100%;\r\n    /* overflow-y: auto; */\r\n}\r\n\r\n.el-tab-pane {\r\n    height: 100%;\r\n    overflow-y: auto;\r\n    overflow-x: hidden;\r\n}\r\n\r\n.chat-staff-card {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    height: 4rem;\r\n    padding: 0 0.75rem;\r\n    margin: 0.25rem 0;\r\n    border-radius: 0.5rem;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.chat-staff-card:hover {\r\n    background-color: #f5f7fa;\r\n    transform: translateX(0.125rem);\r\n}\r\n\r\n.chat-staff-card .profile {\r\n    /* background: linear-gradient(135deg, #1890ff, #0960bd); */\r\n    /* color: #ffffff; */\r\n    width: 2.5rem;\r\n    height: 2.5rem;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    /* border-radius: 0.5rem; */\r\n    margin-right: 0.75rem;\r\n    /* font-weight: 500; */\r\n\r\n}\r\n\r\n.chat-staff-card .profile img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n\r\n}\r\n\r\n\r\n.chat-staff-card .info {\r\n    flex: 1;\r\n    overflow: hidden;\r\n}\r\n\r\n.chat-staff-card .info .name {\r\n    font-size: 0.875rem;\r\n    font-weight: 500;\r\n    color: #2c3e50;\r\n    margin-bottom: 0.25rem;\r\n}\r\n\r\n.chat-staff-card .info .latest-message {\r\n    font-size: 0.75rem;\r\n    color: #909399;\r\n\r\n    width: fit-content;\r\n    /*使宽度适应内容 */\r\n    width: 15ch;\r\n    /*最大宽度为15个字符 */\r\n    white-space: nowrap;\r\n    /*禁止换行 */\r\n    overflow: hidden;\r\n    /*隐藏超出内容 */\r\n    text-overflow: ellipsis;\r\n    /*超出部分用省略号表示 */\r\n}\r\n\r\n.chat-staff-card .latest-message-time {\r\n    font-size: 0.75rem;\r\n    color: #909399;\r\n    width: fit-content;\r\n    /*使宽度适应内容 */\r\n    /* width: 15ch; */\r\n}\r\n\r\n.pageContainer {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 0.75rem 0;\r\n    margin-top: 0.5rem;\r\n    border-top: 0.0625rem solid #f0f0f0;\r\n    width: 100%;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination) {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    padding: 0 0.5rem;\r\n    font-size: 0.8125rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pagination__total) {\r\n    min-width: auto;\r\n    margin-right: 0.5rem;\r\n    font-size: 0.8125rem;\r\n    color: #606266;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager) {\r\n    margin: 0;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager li) {\r\n    min-width: 1.5rem;\r\n    height: 1.5rem;\r\n    line-height: 1.5rem;\r\n    font-weight: normal;\r\n    margin: 0 0.125rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager li.active) {\r\n    background-color: #1890ff;\r\n    color: #ffffff;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination button) {\r\n    min-width: 1.5rem;\r\n    height: 1.5rem;\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .btn-prev),\r\n.pageContainer :deep(.el-pagination .btn-next) {\r\n    margin: 0 0.125rem;\r\n    background-color: #f4f4f5;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-icon) {\r\n    font-size: 0.75rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .more) {\r\n    background-color: transparent;\r\n}\r\n\r\n.employeechatList-tabs {\r\n    height: 100%;\r\n}\r\n\r\n.employeechatList-tabs :deep(.el-tabs__header) {\r\n    margin-bottom: 1rem;\r\n}\r\n\r\n.employeechatList-tabs :deep(.el-tabs__nav-wrap::after) {\r\n    height: 0.0625rem;\r\n    background-color: #f0f0f0;\r\n}\r\n\r\n.employeechatList-tabs :deep(.el-tabs__item) {\r\n    font-size: 0.875rem;\r\n    color: #606266;\r\n    padding: 0 1rem;\r\n}\r\n\r\n.employeechatList-tabs :deep(.el-tabs__item.is-active) {\r\n    color: #1890ff;\r\n    font-weight: 500;\r\n}\r\n\r\n.employeechatList-tabs :deep(.el-tabs__active-bar) {\r\n    background-color: #1890ff;\r\n    height: 0.125rem;\r\n}\r\n\r\n.el-switch {\r\n    --el-switch-off-color: #848487;\r\n}\r\n\r\n.el-switch__core {\r\n    padding: 3px;\r\n}\r\n</style>\r\n", "import script from \"./EmployeeChatList.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./EmployeeChatList.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./EmployeeChatList.vue?vue&type=style&index=0&id=416ecc8c&lang=css\"\n\nconst __exports__ = script;\n\nexport default __exports__"], "names": ["searchWord", "ref", "activeName", "staffList", "totalStaff", "currentPage", "pageSize", "hasCommunicated", "selectedEmployee", "inject", "selectedEmployeeName", "selectedC<PERSON>", "watch", "newValue", "console", "log", "value", "name", "getChatList", "staffChatType", "jwt_token", "localStorage", "getItem", "axiosInstance", "post", "from", "to", "id", "page", "limit", "hasConversation", "headers", "Authorization", "then", "res", "data", "length", "total", "catch", "error", "ElMessage", "handlePageChange", "chatTypeMap", "Map", "chatType", "get", "filterChatList", "warning", "handleClick", "handleChatClick", "staff", "chattype", "fromName", "fromLable", "fromAvatar", "to<PERSON>ame", "<PERSON><PERSON><PERSON><PERSON>", "toLable", "type", "userid", "require", "externalUserId", "avatar", "getCustomerLable", "chatId", "getGroupName", "selectedChat_info", "customer", "corpName", "group", "groupId", "groupName", "slice", "__exports__"], "sourceRoot": ""}