{"version": 3, "file": "js/app.30ed30bd.js", "mappings": "uJACEA,EAAAA,EAAAA,IAA2BC,E,CAK7B,OACEC,KAAM,W,UCAR,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,Q,wCCNA,MAAMC,EAAS,CACX,CACIC,KAAM,aACNJ,KAAM,OACNK,UAAWA,IAAM,+DACjBC,KAAM,CAAEC,cAAc,IAE1B,CACIH,KAAM,SACNJ,KAAM,QACNK,UAAWA,IAAM,sDAErB,CACID,KAAM,mBACNJ,KAAM,kBACNK,UAAWA,IAAM,6BACjBC,KAAM,CAAEC,cAAc,IAE1B,CACIH,KAAM,cACNJ,KAAM,aACNK,UAAWA,IAAM,8BACjBC,KAAM,CAAEC,cAAc,IAG1B,CACIH,KAAM,gBACNJ,KAAM,eACNK,UAAWA,IAAM,sDACjBC,KAAM,CAAEC,cAAc,IAE1B,CACIH,KAAM,gBACNJ,KAAM,eACNK,UAAWA,IAAM,sDACjBC,KAAM,CAAEC,cAAc,IAE1B,CACIH,KAAM,aACNJ,KAAM,YACNK,UAAWA,IAAM,sDACjBC,KAAM,CAAEC,cAAc,IAE1B,CACIH,KAAM,oBACNJ,KAAM,mBACNK,UAAWA,IAAM,+DACjBC,KAAM,CAAEC,cAAc,IAE1B,CACIH,KAAM,oBACNJ,KAAM,mBACNK,UAAWA,IAAM,qDACjBC,KAAM,CAAEC,cAAc,IAE1B,CACIH,KAAM,cACNJ,KAAM,YACNK,UAAWA,IAAM,8BACjBC,KAAM,CAAEC,cAAc,KAMxBC,GAASC,EAAAA,EAAAA,IAAa,CAExBC,SAASC,EAAAA,EAAAA,MACTR,WAIJK,EAAOI,YAAW,CAACC,EAAIC,EAAMC,KAEzB,MAAMC,EAAOC,aAAaC,QAAQ,QAC5BC,EAAaF,aAAaC,QAAQ,cAExCE,QAAQC,IAAI,8BAA+BL,GAC3CI,QAAQC,IAAI,oCAAqCF,GACjDC,QAAQC,IAAI,iBAAkBC,KAAKC,OAE/BV,EAAGP,KAAKC,aACHS,EAGMG,EAAaG,KAAKC,OACzBH,QAAQC,IAAI,kBAAmBL,GAC/BC,aAAaO,WAAW,QACxBP,aAAaO,WAAW,cACxBT,EAAK,YAELK,QAAQC,IAAI,kBAAmBL,GAC/BD,MATAK,QAAQC,IAAI,cAAeL,GAC3BD,EAAK,WAUU,WAAZF,EAAGT,MAAqBY,GAAQG,EAAaG,KAAKC,OACzDH,QAAQC,IAAI,8BAA+BL,GAC3CD,EAAK,OAELK,QAAQC,IAAI,oBAAqBL,GACjCD,IACJ,IAGJ,Q,SCvFA,MAAMU,GAAMC,EAAAA,EAAAA,IAAUC,GACtBF,EAAIG,IAAIC,EAAAA,GACRJ,EAAIG,IAAIE,GACRL,EAAIG,IAAIG,GACRN,EAAIG,IAAII,EAAAA,IAERP,EAAIQ,MAAM,O,GCxBNC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUM,KAAKF,EAAOD,QAASC,EAAQA,EAAOD,QAASJ,GAGpEK,EAAOD,OACf,CAGAJ,EAAoBQ,EAAIF,E,WCzBxB,IAAIG,EAAW,GACfT,EAAoBU,EAAI,SAASC,EAAQC,EAAUC,EAAIC,GACtD,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIR,EAASS,OAAQD,IAAK,CACrCL,EAAWH,EAASQ,GAAG,GACvBJ,EAAKJ,EAASQ,GAAG,GACjBH,EAAWL,EAASQ,GAAG,GAE3B,IAJA,IAGIE,GAAY,EACPC,EAAI,EAAGA,EAAIR,EAASM,OAAQE,MACpB,EAAXN,GAAsBC,GAAgBD,IAAaO,OAAOC,KAAKtB,EAAoBU,GAAGa,OAAM,SAASC,GAAO,OAAOxB,EAAoBU,EAAEc,GAAKZ,EAASQ,GAAK,IAChKR,EAASa,OAAOL,IAAK,IAErBD,GAAY,EACTL,EAAWC,IAAcA,EAAeD,IAG7C,GAAGK,EAAW,CACbV,EAASgB,OAAOR,IAAK,GACrB,IAAIS,EAAIb,SACEV,IAANuB,IAAiBf,EAASe,EAC/B,CACD,CACA,OAAOf,CArBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIR,EAASS,OAAQD,EAAI,GAAKR,EAASQ,EAAI,GAAG,GAAKH,EAAUG,IAAKR,EAASQ,GAAKR,EAASQ,EAAI,GACrGR,EAASQ,GAAK,CAACL,EAAUC,EAAIC,EAwB/B,C,eC5BAd,EAAoB2B,EAAI,SAAStB,GAChC,IAAIuB,EAASvB,GAAUA,EAAOwB,WAC7B,WAAa,OAAOxB,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAL,EAAoB8B,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CACR,C,eCNA5B,EAAoB8B,EAAI,SAAS1B,EAAS4B,GACzC,IAAI,IAAIR,KAAOQ,EACXhC,EAAoBiC,EAAED,EAAYR,KAASxB,EAAoBiC,EAAE7B,EAASoB,IAC5EH,OAAOa,eAAe9B,EAASoB,EAAK,CAAEW,YAAY,EAAMC,IAAKJ,EAAWR,IAG3E,C,eCPAxB,EAAoBqC,EAAI,CAAC,EAGzBrC,EAAoBsC,EAAI,SAASC,GAChC,OAAOC,QAAQC,IAAIpB,OAAOC,KAAKtB,EAAoBqC,GAAGK,QAAO,SAASC,EAAUnB,GAE/E,OADAxB,EAAoBqC,EAAEb,GAAKe,EAASI,GAC7BA,CACR,GAAG,IACJ,C,eCPA3C,EAAoB4C,EAAI,SAASL,GAEhC,MAAO,MAAQA,EAAU,IAAM,CAAC,GAAK,WAAW,GAAK,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,KACtO,C,eCHAvC,EAAoB6C,SAAW,SAASN,GAEvC,MAAO,OAASA,EAAU,IAAM,CAAC,GAAK,WAAW,GAAK,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,MACtN,C,eCJAvC,EAAoB8C,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,EAChB,CAAE,MAAOX,GACR,GAAsB,kBAAXY,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxBlD,EAAoBiC,EAAI,SAASkB,EAAKC,GAAQ,OAAO/B,OAAOgC,UAAUC,eAAe/C,KAAK4C,EAAKC,EAAO,C,eCAtG,IAAIG,EAAa,CAAC,EACdC,EAAoB,aAExBxD,EAAoByD,EAAI,SAASC,EAAKC,EAAMnC,EAAKe,GAChD,GAAGgB,EAAWG,GAAQH,EAAWG,GAAKE,KAAKD,OAA3C,CACA,IAAIE,EAAQC,EACZ,QAAW3D,IAARqB,EAEF,IADA,IAAIuC,EAAUC,SAASC,qBAAqB,UACpChD,EAAI,EAAGA,EAAI8C,EAAQ7C,OAAQD,IAAK,CACvC,IAAIiD,EAAIH,EAAQ9C,GAChB,GAAGiD,EAAEC,aAAa,QAAUT,GAAOQ,EAAEC,aAAa,iBAAmBX,EAAoBhC,EAAK,CAAEqC,EAASK,EAAG,KAAO,CACpH,CAEGL,IACHC,GAAa,EACbD,EAASG,SAASI,cAAc,UAEhCP,EAAOQ,QAAU,QACjBR,EAAOS,QAAU,IACbtE,EAAoBuE,IACvBV,EAAOW,aAAa,QAASxE,EAAoBuE,IAElDV,EAAOW,aAAa,eAAgBhB,EAAoBhC,GAExDqC,EAAOY,IAAMf,GAEdH,EAAWG,GAAO,CAACC,GACnB,IAAIe,EAAmB,SAASC,EAAMC,GAErCf,EAAOgB,QAAUhB,EAAOiB,OAAS,KACjCC,aAAaT,GACb,IAAIU,EAAUzB,EAAWG,GAIzB,UAHOH,EAAWG,GAClBG,EAAOoB,YAAcpB,EAAOoB,WAAWC,YAAYrB,GACnDmB,GAAWA,EAAQG,SAAQ,SAAStE,GAAM,OAAOA,EAAG+D,EAAQ,IACzDD,EAAM,OAAOA,EAAKC,EACtB,EACIN,EAAUc,WAAWV,EAAiBW,KAAK,UAAMlF,EAAW,CAAEmF,KAAM,UAAWC,OAAQ1B,IAAW,MACtGA,EAAOgB,QAAUH,EAAiBW,KAAK,KAAMxB,EAAOgB,SACpDhB,EAAOiB,OAASJ,EAAiBW,KAAK,KAAMxB,EAAOiB,QACnDhB,GAAcE,SAASwB,KAAKC,YAAY5B,EApCkB,CAqC3D,C,eCxCA7D,EAAoB0B,EAAI,SAAStB,GACX,qBAAXsF,QAA0BA,OAAOC,aAC1CtE,OAAOa,eAAe9B,EAASsF,OAAOC,YAAa,CAAEC,MAAO,WAE7DvE,OAAOa,eAAe9B,EAAS,aAAc,CAAEwF,OAAO,GACvD,C,eCNA5F,EAAoB6F,EAAI,a,eCAxB,GAAwB,qBAAb7B,SAAX,CACA,IAAI8B,EAAmB,SAASvD,EAASwD,EAAUC,EAAQC,EAASC,GACnE,IAAIC,EAAUnC,SAASI,cAAc,QAErC+B,EAAQC,IAAM,aACdD,EAAQb,KAAO,WACXtF,EAAoBuE,KACvB4B,EAAQE,MAAQrG,EAAoBuE,IAErC,IAAI+B,EAAiB,SAAS1B,GAG7B,GADAuB,EAAQtB,QAAUsB,EAAQrB,OAAS,KAChB,SAAfF,EAAMU,KACTW,QACM,CACN,IAAIM,EAAY3B,GAASA,EAAMU,KAC3BkB,EAAW5B,GAASA,EAAMW,QAAUX,EAAMW,OAAOkB,MAAQV,EACzDW,EAAM,IAAIC,MAAM,qBAAuBpE,EAAU,cAAgBgE,EAAY,KAAOC,EAAW,KACnGE,EAAI7I,KAAO,iBACX6I,EAAIE,KAAO,wBACXF,EAAIpB,KAAOiB,EACXG,EAAIG,QAAUL,EACVL,EAAQlB,YAAYkB,EAAQlB,WAAWC,YAAYiB,GACvDD,EAAOQ,EACR,CACD,EAUA,OATAP,EAAQtB,QAAUsB,EAAQrB,OAASwB,EACnCH,EAAQM,KAAOV,EAGXC,EACHA,EAAOf,WAAW6B,aAAaX,EAASH,EAAOe,aAE/C/C,SAASwB,KAAKC,YAAYU,GAEpBA,CACR,EACIa,EAAiB,SAASP,EAAMV,GAEnC,IADA,IAAIkB,EAAmBjD,SAASC,qBAAqB,QAC7ChD,EAAI,EAAGA,EAAIgG,EAAiB/F,OAAQD,IAAK,CAChD,IAAIiG,EAAMD,EAAiBhG,GACvBkG,EAAWD,EAAI/C,aAAa,cAAgB+C,EAAI/C,aAAa,QACjE,GAAe,eAAZ+C,EAAId,MAAyBe,IAAaV,GAAQU,IAAapB,GAAW,OAAOmB,CACrF,CACA,IAAIE,EAAoBpD,SAASC,qBAAqB,SACtD,IAAQhD,EAAI,EAAGA,EAAImG,EAAkBlG,OAAQD,IAAK,CAC7CiG,EAAME,EAAkBnG,GACxBkG,EAAWD,EAAI/C,aAAa,aAChC,GAAGgD,IAAaV,GAAQU,IAAapB,EAAU,OAAOmB,CACvD,CACD,EACIG,EAAiB,SAAS9E,GAC7B,OAAO,IAAIC,SAAQ,SAASyD,EAASC,GACpC,IAAIO,EAAOzG,EAAoB6C,SAASN,GACpCwD,EAAW/F,EAAoB6F,EAAIY,EACvC,GAAGO,EAAeP,EAAMV,GAAW,OAAOE,IAC1CH,EAAiBvD,EAASwD,EAAU,KAAME,EAASC,EACpD,GACD,EAEIoB,EAAqB,CACxB,IAAK,GAGNtH,EAAoBqC,EAAEkF,QAAU,SAAShF,EAASI,GACjD,IAAI6E,EAAY,CAAC,GAAK,EAAE,GAAK,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,GAC1FF,EAAmB/E,GAAUI,EAASiB,KAAK0D,EAAmB/E,IACzB,IAAhC+E,EAAmB/E,IAAkBiF,EAAUjF,IACtDI,EAASiB,KAAK0D,EAAmB/E,GAAW8E,EAAe9E,GAASkF,MAAK,WACxEH,EAAmB/E,GAAW,CAC/B,IAAG,SAASD,GAEX,aADOgF,EAAmB/E,GACpBD,CACP,IAEF,CA3E2C,C,eCK3C,IAAIoF,EAAkB,CACrB,IAAK,GAGN1H,EAAoBqC,EAAEjB,EAAI,SAASmB,EAASI,GAE1C,IAAIgF,EAAqB3H,EAAoBiC,EAAEyF,EAAiBnF,GAAWmF,EAAgBnF,QAAWpC,EACtG,GAA0B,IAAvBwH,EAGF,GAAGA,EACFhF,EAASiB,KAAK+D,EAAmB,SAEjC,GAAG,KAAOpF,EAAS,CAElB,IAAIqF,EAAU,IAAIpF,SAAQ,SAASyD,EAASC,GAAUyB,EAAqBD,EAAgBnF,GAAW,CAAC0D,EAASC,EAAS,IACzHvD,EAASiB,KAAK+D,EAAmB,GAAKC,GAGtC,IAAIlE,EAAM1D,EAAoB6F,EAAI7F,EAAoB4C,EAAEL,GAEpDsF,EAAQ,IAAIlB,MACZmB,EAAe,SAASlD,GAC3B,GAAG5E,EAAoBiC,EAAEyF,EAAiBnF,KACzCoF,EAAqBD,EAAgBnF,GACX,IAAvBoF,IAA0BD,EAAgBnF,QAAWpC,GACrDwH,GAAoB,CACtB,IAAIpB,EAAY3B,IAAyB,SAAfA,EAAMU,KAAkB,UAAYV,EAAMU,MAChEyC,EAAUnD,GAASA,EAAMW,QAAUX,EAAMW,OAAOd,IACpDoD,EAAMG,QAAU,iBAAmBzF,EAAU,cAAgBgE,EAAY,KAAOwB,EAAU,IAC1FF,EAAMhK,KAAO,iBACbgK,EAAMvC,KAAOiB,EACbsB,EAAMhB,QAAUkB,EAChBJ,EAAmB,GAAGE,EACvB,CAEF,EACA7H,EAAoByD,EAAEC,EAAKoE,EAAc,SAAWvF,EAASA,EAC9D,MAAOmF,EAAgBnF,GAAW,CAGtC,EAUAvC,EAAoBU,EAAEU,EAAI,SAASmB,GAAW,OAAoC,IAA7BmF,EAAgBnF,EAAgB,EAGrF,IAAI0F,EAAuB,SAASC,EAA4BC,GAC/D,IAKIlI,EAAUsC,EALV3B,EAAWuH,EAAK,GAChBC,EAAcD,EAAK,GACnBE,EAAUF,EAAK,GAGIlH,EAAI,EAC3B,GAAGL,EAAS0H,MAAK,SAASC,GAAM,OAA+B,IAAxBb,EAAgBa,EAAW,IAAI,CACrE,IAAItI,KAAYmI,EACZpI,EAAoBiC,EAAEmG,EAAanI,KACrCD,EAAoBQ,EAAEP,GAAYmI,EAAYnI,IAGhD,GAAGoI,EAAS,IAAI1H,EAAS0H,EAAQrI,EAClC,CAEA,IADGkI,GAA4BA,EAA2BC,GACrDlH,EAAIL,EAASM,OAAQD,IACzBsB,EAAU3B,EAASK,GAChBjB,EAAoBiC,EAAEyF,EAAiBnF,IAAYmF,EAAgBnF,IACrEmF,EAAgBnF,GAAS,KAE1BmF,EAAgBnF,GAAW,EAE5B,OAAOvC,EAAoBU,EAAEC,EAC9B,EAEI6H,EAAqBC,KAAK,yBAA2BA,KAAK,0BAA4B,GAC1FD,EAAmBrD,QAAQ8C,EAAqB5C,KAAK,KAAM,IAC3DmD,EAAmB5E,KAAOqE,EAAqB5C,KAAK,KAAMmD,EAAmB5E,KAAKyB,KAAKmD,G,ICpFvF,IAAIE,EAAsB1I,EAAoBU,OAAEP,EAAW,CAAC,MAAM,WAAa,OAAOH,EAAoB,KAAO,IACjH0I,EAAsB1I,EAAoBU,EAAEgI,E", "sources": ["webpack://wccsfront/./src/App.vue", "webpack://wccsfront/./src/App.vue?7ccd", "webpack://wccsfront/./src/router/index.js", "webpack://wccsfront/./src/main.js", "webpack://wccsfront/webpack/bootstrap", "webpack://wccsfront/webpack/runtime/chunk loaded", "webpack://wccsfront/webpack/runtime/compat get default export", "webpack://wccsfront/webpack/runtime/define property getters", "webpack://wccsfront/webpack/runtime/ensure chunk", "webpack://wccsfront/webpack/runtime/get javascript chunk filename", "webpack://wccsfront/webpack/runtime/get mini-css chunk filename", "webpack://wccsfront/webpack/runtime/global", "webpack://wccsfront/webpack/runtime/hasOwnProperty shorthand", "webpack://wccsfront/webpack/runtime/load script", "webpack://wccsfront/webpack/runtime/make namespace object", "webpack://wccsfront/webpack/runtime/publicPath", "webpack://wccsfront/webpack/runtime/css loading", "webpack://wccsfront/webpack/runtime/jsonp chunk loading", "webpack://wccsfront/webpack/startup"], "sourcesContent": ["<template>\n  <router-view></router-view>\n</template>\n\n<script>\n\nexport default {\n  name: '<PERSON>App',\n};\n\n</script>\n\n\n<style>\n/* #app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 60px;\n} */\n</style>\n", "import { render } from \"./App.vue?vue&type=template&id=487f7c4c\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\n\nimport \"./App.vue?vue&type=style&index=0&id=487f7c4c&lang=css\"\n\nimport exportComponent from \"../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "// import full from 'core-js/full';\r\nimport { createRouter, createWebHistory } from 'vue-router';\r\n\r\nconst routes = [\r\n    {\r\n        path: '/wxArchive',\r\n        name: 'Home',\r\n        component: () => import('@/homePage/HomePage.vue'),\r\n        meta: { requiresAuth: true }, // 该路由需要身份验证\r\n    },\r\n    {\r\n        path: '/login',\r\n        name: 'Login',\r\n        component: () => import('@/loginPage/login.vue'),\r\n    },\r\n    {\r\n        path: '/functionbarfold',\r\n        name: 'FunctionBarFold',\r\n        component: () => import('@/components/FunctionBarFold.vue'),\r\n        meta: { requiresAuth: true }, // 该路由需要身份验证\r\n    },\r\n    {\r\n        path: '/breadcrumb',\r\n        name: 'Breadcrumb',\r\n        component: () => import('@/components/BreadCrumb.vue'),\r\n        meta: { requiresAuth: true }, // 该路由需要身份验证\r\n    },\r\n\r\n    {\r\n        path: '/employeelist',\r\n        name: 'EmployeeList',\r\n        component: () => import('@/components/ChatArchive/EmployeeChat/EmployeeList.vue'),\r\n        meta: { requiresAuth: true }, // 该路由需要身份验证\r\n    },\r\n    {\r\n        path: '/customerlist',\r\n        name: 'CustomerList',\r\n        component: () => import('@/components/ChatArchive/CustomerChat/CustomerList.vue'),\r\n        meta: { requiresAuth: true }, // 该路由需要身份验证\r\n    },\r\n    {\r\n        path: '/grouplist',\r\n        name: 'GroupList',\r\n        component: () => import('@/components/ChatArchive/GroupChat/GroupList.vue'),\r\n        meta: { requiresAuth: true }, // 该路由需要身份验证\r\n    },\r\n    {\r\n        path: '/employeechatlist',\r\n        name: 'EmployeeChatList',\r\n        component: () => import('@/components/ChatArchive/EmployeeChat/EmployeeChatList.vue'),\r\n        meta: { requiresAuth: true }, // 该路由需要身份验证\r\n    },\r\n    {\r\n        path: '/customerchatlist',\r\n        name: 'CustomerChatList',\r\n        component: () => import('@/components/ChatArchive/CustomerChat/CustomerChatList.vue'),\r\n        meta: { requiresAuth: true }, // 该路由需要身份验证\r\n    },\r\n    {\r\n        path: '/voice-test',\r\n        name: 'VoiceTest',\r\n        component: () => import('@/components/VoiceMessageTest.vue'),\r\n        meta: { requiresAuth: false }, // 测试页面不需要身份验证\r\n    },\r\n\r\n];\r\n\r\n\r\nconst router = createRouter({\r\n    // history: createWebHashHistory(),\r\n    history: createWebHistory(),\r\n    routes\r\n});\r\n\r\n// 全局路由守卫\r\nrouter.beforeEach((to, from, next) => {\r\n\r\n    const user = localStorage.getItem('user');\r\n    const expireTime = localStorage.getItem('expireTime');\r\n\r\n    console.log('全局路由守卫--获取localStorage的user', user)\r\n    console.log('全局路由守卫--获取localStorage的expireTime', expireTime)\r\n    console.log('全局路由守卫--获取当前时间', Date.now())\r\n\r\n    if (to.meta.requiresAuth) {\r\n        if (!user) {\r\n            console.log('全局路由守卫--未登录', user);\r\n            next('/login');\r\n        } else if (expireTime < Date.now()) {\r\n            console.log('全局路由守卫--已登录，已过期', user);\r\n            localStorage.removeItem('user');\r\n            localStorage.removeItem('expireTime');\r\n            next('/login');\r\n        } else {\r\n            console.log('全局路由守卫--已登录，未过期', user);\r\n            next();\r\n        }\r\n    } else if (to.path === '/login' && user && expireTime > Date.now()) {\r\n        console.log('全局路由守卫--已登录，未过期，访问登录页，跳转到首页', user);\r\n        next('/');\r\n    } else {\r\n        console.log('全局路由守卫--无需登录或正常访问', user);\r\n        next();\r\n    }\r\n});\r\n\r\nexport default router;\r\n\r\n", "import { createApp } from 'vue'\nimport App from './App.vue'\n\n//引入element-ui\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport * as ElementPlusIcons from '@element-plus/icons-vue'\n\n//引入路由\nimport VueRouter from './router'\n\n//引入reset.css\nimport 'reset-css/reset.css'\n\n//引入vue-audio-visual\nimport { AVPlugin } from 'vue-audio-visual'\n\n\n\nconst app = createApp(App)\napp.use(ElementPlus)\napp.use(VueRouter)\napp.use(ElementPlusIcons)\napp.use(AVPlugin)\n\napp.mount('#app')\n\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = function(chunkId) {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce(function(promises, key) {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"js/\" + chunkId + \".\" + {\"43\":\"a9563591\",\"67\":\"88632576\",\"124\":\"38b9cc24\",\"147\":\"520f9b74\",\"314\":\"1ce42797\",\"548\":\"af69a3b7\",\"623\":\"21cbe9c9\",\"752\":\"efe0089d\",\"843\":\"d13b467c\",\"954\":\"90097c6e\",\"978\":\"2183276d\"}[chunkId] + \".js\";\n};", "// This function allow to reference async chunks\n__webpack_require__.miniCssF = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"css/\" + chunkId + \".\" + {\"43\":\"773fb6c9\",\"67\":\"11dd6865\",\"124\":\"004fe65c\",\"147\":\"2764a41f\",\"306\":\"c370a175\",\"548\":\"c6a5577e\",\"623\":\"95bec1f8\",\"752\":\"b512a6d4\",\"954\":\"c0c78dbf\",\"978\":\"ee8bdee3\"}[chunkId] + \".css\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "var inProgress = {};\nvar dataWebpackPrefix = \"wccsfront:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = function(url, done, key, chunkId) {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = function(prev, event) {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach(function(fn) { return fn(event); });\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.p = \"/wxArchive/\";", "if (typeof document === \"undefined\") return;\nvar createStylesheet = function(chunkId, fullhref, oldTag, resolve, reject) {\n\tvar linkTag = document.createElement(\"link\");\n\n\tlinkTag.rel = \"stylesheet\";\n\tlinkTag.type = \"text/css\";\n\tif (__webpack_require__.nc) {\n\t\tlinkTag.nonce = __webpack_require__.nc;\n\t}\n\tvar onLinkComplete = function(event) {\n\t\t// avoid mem leaks.\n\t\tlinkTag.onerror = linkTag.onload = null;\n\t\tif (event.type === 'load') {\n\t\t\tresolve();\n\t\t} else {\n\t\t\tvar errorType = event && event.type;\n\t\t\tvar realHref = event && event.target && event.target.href || fullhref;\n\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + errorType + \": \" + realHref + \")\");\n\t\t\terr.name = \"ChunkLoadError\";\n\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n\t\t\terr.type = errorType;\n\t\t\terr.request = realHref;\n\t\t\tif (linkTag.parentNode) linkTag.parentNode.removeChild(linkTag)\n\t\t\treject(err);\n\t\t}\n\t}\n\tlinkTag.onerror = linkTag.onload = onLinkComplete;\n\tlinkTag.href = fullhref;\n\n\n\tif (oldTag) {\n\t\toldTag.parentNode.insertBefore(linkTag, oldTag.nextSibling);\n\t} else {\n\t\tdocument.head.appendChild(linkTag);\n\t}\n\treturn linkTag;\n};\nvar findStylesheet = function(href, fullhref) {\n\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n\tfor(var i = 0; i < existingLinkTags.length; i++) {\n\t\tvar tag = existingLinkTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return tag;\n\t}\n\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n\tfor(var i = 0; i < existingStyleTags.length; i++) {\n\t\tvar tag = existingStyleTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\");\n\t\tif(dataHref === href || dataHref === fullhref) return tag;\n\t}\n};\nvar loadStylesheet = function(chunkId) {\n\treturn new Promise(function(resolve, reject) {\n\t\tvar href = __webpack_require__.miniCssF(chunkId);\n\t\tvar fullhref = __webpack_require__.p + href;\n\t\tif(findStylesheet(href, fullhref)) return resolve();\n\t\tcreateStylesheet(chunkId, fullhref, null, resolve, reject);\n\t});\n}\n// object to store loaded CSS chunks\nvar installedCssChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.miniCss = function(chunkId, promises) {\n\tvar cssChunks = {\"43\":1,\"67\":1,\"124\":1,\"147\":1,\"306\":1,\"548\":1,\"623\":1,\"752\":1,\"954\":1,\"978\":1};\n\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n\t\tpromises.push(installedCssChunks[chunkId] = loadStylesheet(chunkId).then(function() {\n\t\t\tinstalledCssChunks[chunkId] = 0;\n\t\t}, function(e) {\n\t\t\tdelete installedCssChunks[chunkId];\n\t\t\tthrow e;\n\t\t}));\n\t}\n};\n\n// no hmr\n\n// no prefetching\n\n// no preloaded", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.j = function(chunkId, promises) {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(306 != chunkId) {\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise(function(resolve, reject) { installedChunkData = installedChunks[chunkId] = [resolve, reject]; });\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = function(event) {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t} else installedChunks[chunkId] = 0;\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkwccsfront\"] = self[\"webpackChunkwccsfront\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], function() { return __webpack_require__(1502); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["_createBlock", "_component_router_view", "name", "__exports__", "render", "routes", "path", "component", "meta", "requiresAuth", "router", "createRouter", "history", "createWebHistory", "beforeEach", "to", "from", "next", "user", "localStorage", "getItem", "expireTime", "console", "log", "Date", "now", "removeItem", "app", "createApp", "App", "use", "ElementPlus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPlusIcons", "<PERSON><PERSON><PERSON><PERSON>", "mount", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "call", "m", "deferred", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "length", "fulfilled", "j", "Object", "keys", "every", "key", "splice", "r", "n", "getter", "__esModule", "d", "a", "definition", "o", "defineProperty", "enumerable", "get", "f", "e", "chunkId", "Promise", "all", "reduce", "promises", "u", "miniCssF", "g", "globalThis", "this", "Function", "window", "obj", "prop", "prototype", "hasOwnProperty", "inProgress", "dataWebpackPrefix", "l", "url", "done", "push", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "s", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "src", "onScriptComplete", "prev", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "setTimeout", "bind", "type", "target", "head", "append<PERSON><PERSON><PERSON>", "Symbol", "toStringTag", "value", "p", "createStylesheet", "fullhref", "oldTag", "resolve", "reject", "linkTag", "rel", "nonce", "onLinkComplete", "errorType", "realHref", "href", "err", "Error", "code", "request", "insertBefore", "nextS<PERSON>ling", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "existingLinkTags", "tag", "dataHref", "existingStyleTags", "loadStylesheet", "installedCssChunks", "miniCss", "cssChunks", "then", "installedChunks", "installedChunkData", "promise", "error", "loadingEnded", "realSrc", "message", "webpackJsonpCallback", "parentChunkLoadingFunction", "data", "moreModules", "runtime", "some", "id", "chunkLoadingGlobal", "self", "__webpack_exports__"], "sourceRoot": ""}