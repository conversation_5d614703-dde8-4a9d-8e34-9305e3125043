"use strict";(self["webpackChunkwccsfront"]=self["webpackChunkwccsfront"]||[]).push([[978],{8978:function(e,a,t){t.r(a),t.d(a,{default:function(){return b}});var l=t(6768),s=t(144),n=t(4232),r=t(7477),u=t(1314),c=t(1219);const o={class:"mainContainer-customerlist"},i={class:"search",style:{width:"800px"}},v={class:"lable-navigator"},d=["onClick"],p={class:"profile"},m=["src"],g={class:"info"},k={class:"name"},h={class:"pageContainer"};var C={__name:"CustomerList",setup(e){const a=(0,s.KR)(""),t=(0,s.KR)("first"),C=(0,s.KR)([]),f=(0,s.KR)(""),b=(0,s.KR)(1),L=(0,s.KR)(100),w=(0,l.WQ)("currentComponent_List"),_=(0,l.WQ)("currentComponent_ChatList"),y=(e,a,t)=>{w.value="CustomerList",_.value="CustomerChatList";const l=localStorage.getItem("access_token");u.A.post("/api/chatmessage/chatter",{type:"customer",page:e,limit:a,name:t},{headers:{Authorization:"Bearer "+l}}).then((e=>{0===e.data.code&&(C.value=e.data.data.result,f.value=e.data.data.total)})).catch((e=>{console.log(e),c.nk.error("获取客户列表失败，请检查网络或联系管理员")}))},R=(0,l.EW)((()=>C.value));(0,l.wB)(R,(e=>{console.log("当页客户总数",e.length)}));const F=e=>{b.value=e,y(b.value,L.value,a.value)},K=()=>{a.value&&(b.value=1),y(b.value,L.value,a.value)};(0,l.sV)((()=>y(1,100,""))),(0,l.wB)(a,K);const V=(0,l.WQ)("selectedCustomer",(0,s.KR)(null)),z=e=>{V&&(V.value={name:e.name,avatar:e.avatar,label:x(e),id:e.externalUserId||""}),console.log("选中员工:",e.name)},Q=(e,a)=>{console.log(e,a)},U=e=>{switch(e){case 1:return"wechat";case 2:return"ewechat";default:return"unknown"}},x=e=>{switch(e.type){case 1:return"wmykZGPQAApQrCvSTyJRFBdyOQrDpjUg"==e.externalUserId&&console.log("触发点7"),"@微信";case 2:return e.corpName?`@${e.corpName}`:"@未知企业";default:return""}};return(e,u)=>{const c=(0,l.g2)("el-icon"),C=(0,l.g2)("el-input"),w=(0,l.g2)("el-tab-pane"),_=(0,l.g2)("el-tabs"),y=(0,l.g2)("el-pagination");return(0,l.uX)(),(0,l.CE)("div",o,[(0,l.Lk)("div",i,[(0,l.bF)(C,{placeholder:"搜索客户",modelValue:a.value,"onUpdate:modelValue":u[0]||(u[0]=e=>a.value=e),clearable:"",class:"search-input"},{prefix:(0,l.k6)((()=>[(0,l.bF)(c,{class:"search-icon"},{default:(0,l.k6)((()=>[(0,l.bF)((0,s.R1)(r.Search))])),_:1})])),_:1},8,["modelValue"])]),(0,l.Lk)("div",v,[(0,l.bF)(_,{modelValue:t.value,"onUpdate:modelValue":u[1]||(u[1]=e=>t.value=e),class:"customerList-tabs",onTabClick:Q},{default:(0,l.k6)((()=>[(0,l.bF)(w,{label:"按客户",name:"first"},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(R.value,(e=>((0,l.uX)(),(0,l.CE)("div",{class:"customer-card",key:e.name,onClick:a=>z(e)},[(0,l.Lk)("div",p,[(0,l.Lk)("img",{class:"staff-avatar",src:e.avatar,alt:"人员头像"},null,8,m)]),(0,l.Lk)("div",g,[(0,l.Lk)("p",k,(0,n.v_)(e.name),1),(0,l.Lk)("p",{class:(0,n.C4)(["lable",U(e.type)])},(0,n.v_)(x(e)),3)])],8,d)))),128))])),_:1})])),_:1},8,["modelValue"])]),(0,l.Lk)("div",h,[(0,l.bF)(y,{"current-page":b.value,"onUpdate:currentPage":u[2]||(u[2]=e=>b.value=e),background:"",size:e.size,layout:"total, prev, pager, next",total:f.value,"page-size":L.value,"pager-count":3,small:"",onCurrentChange:F},null,8,["current-page","size","total","page-size"])])])}}};const f=C;var b=f}}]);
//# sourceMappingURL=978.039fe18f.js.map