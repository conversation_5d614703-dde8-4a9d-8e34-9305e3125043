{"version": 3, "file": "js/623.e5699fb2.js", "mappings": "oRAcA,MAAMA,GAAkBC,EAAAA,EAAAA,IAAO,mB,wYCT/B,MAAMC,EAAc,EAEpB,O", "sources": ["webpack://wccsfront/./src/components/BreadCrumb.vue", "webpack://wccsfront/./src/components/BreadCrumb.vue?e3af"], "sourcesContent": ["<template>\r\n    <div class=\"breadcrumb-container\">\r\n        <el-breadcrumb :separator-icon=\"ArrowRight\">\r\n            <el-breadcrumb-item v-for=\"(item, index) in breadcrumbItems\" :key=\"index\">\r\n                {{ item }}\r\n            </el-breadcrumb-item>\r\n        </el-breadcrumb>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ArrowRight } from '@element-plus/icons-vue'\r\nimport { inject } from 'vue'\r\n\r\nconst breadcrumbItems = inject('breadcrumbItems')\r\n\r\n</script>\r\n\r\n<style>\r\n.breadcrumb-container {\r\n    margin-bottom: 5px;\r\n}\r\n</style>", "import script from \"./BreadCrumb.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./BreadCrumb.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./BreadCrumb.vue?vue&type=style&index=0&id=50b047ce&lang=css\"\n\nconst __exports__ = script;\n\nexport default __exports__"], "names": ["breadcrumbItems", "inject", "__exports__"], "sourceRoot": ""}