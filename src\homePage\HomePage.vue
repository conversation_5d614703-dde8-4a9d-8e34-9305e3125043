<template>
  <div class="mainContainer">
    <div class="nabigationBar">
      <div class="left">
        <img class="logo" src="../assets/仓鼠.png" alt="系统图标" />
        <h2>会话存档平台</h2>
      </div>

      <el-popover :visible="visible" placement="bottom" trigger="hover" width="90"
        popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; display: flex; justify-content: center; align-items: center; padding: 5px 10px; background-color: #fff; border-radius: 4px;">
        <div class="logout-Btn">
          <el-button @click="handleLogout"
            style="height: 18px; width: 90px; font-size: 12px; border: none;">点击退出登录</el-button>
        </div>

        <template #reference>
          <div class="right" ref="userLogout">
            <el-divider direction="vertical"></el-divider>
            <h4>当前用户：{{ user }}</h4>
          </div>
        </template>

      </el-popover>

    </div>

    <div class="mainContent">
      <div class="functionBar">
        <!-- 引入 FunctionBarFold 组件 -->
        <FunctionBarFold />
      </div>
      <div class="mainContent-right">
        <div class="breadcrumb" style="margin: 2px 0;">
          <!-- 引入 Breadcrumb 组件 -->
          <Breadcrumb />
        </div>
        <div class="functionPad">
          <!-- 引入 EmployeeList/CustomerList 组件 -->
          <component :is="components[currentComponent_List]"></component>

          <component :is="components[currentComponent_ChatList]"></component>

          <component v-if="currentComponent_DetailChatBoard" :is="DetailChatBoard"></component>

        </div>
      </div>
    </div>

  </div>

</template>

<script setup>
import { ref, provide } from 'vue'
import { useRouter } from 'vue-router'
import FunctionBarFold from '../components/FunctionBarFold.vue'
import Breadcrumb from '../components/BreadCrumb.vue'

import EmployeeList from '../components/ChatArchive/EmployeeChat/EmployeeList.vue'
import CustomerList from '../components/ChatArchive/CustomerChat/CustomerList.vue'
import GroupList from '../components/ChatArchive/GroupChat/GroupList.vue'

import EmployeeChatList from '../components/ChatArchive/EmployeeChat/EmployeeChatList.vue'
import CustomerChatList from '../components/ChatArchive/CustomerChat/CustomerChatList.vue'

import DetailChatBoard from '../components/DetailChatBoard.vue'

// 注册动态组件
const components = {
  EmployeeList,
  CustomerList,
  GroupList,
  EmployeeChatList,
  CustomerChatList,
  DetailChatBoard
}

const router = useRouter()

// 界面右上角用户标识
const user = ref(localStorage.getItem('user'))

// 面包屑导航
const breadcrumbItems = ref(['会话存档', '员工会话'])
provide('breadcrumbItems', breadcrumbItems)

// 面板切换
const currentComponent_List = ref('EmployeeList')
provide('currentComponent_List', currentComponent_List)

const currentComponent_ChatList = ref('EmployeeChatList')
provide('currentComponent_ChatList', currentComponent_ChatList)

const currentComponent_DetailChatBoard = ref('DetailChatBoard')

// 创建一个响应式变量存储当前选中的功能的index
const currentFunction = ref('currentFunction')
provide('currentFunction', currentFunction)

// 创建一个响应式变量用于存储侧边功能面板的收缩/折叠状态
const FunctionBar_isCollapse = ref('true')
provide('FunctionBar_isCollapse', FunctionBar_isCollapse)

// 创建一个响应式变量用于存储选中的员工信息
const selectedEmployee = ref(null)
provide('selectedEmployee', selectedEmployee)

// 创建一个响应式变量用于存储选中的客户信息
const selectedCustomer = ref(null)
provide('selectedCustomer', selectedCustomer)

// 创建一个响应式变量用于调用会话详情接口的参数信息
const selectedChat = ref({
  from: null,
  to: null,

  fromName: null,
  fromLable: null,
  fromAvatar: null,

  toName: null,
  toAvatar: null,
  toLable: null,

  type: "",
  page: 1,
  limit: 30
})
provide('selectedChat', selectedChat)


// 登出处理
const handleLogout = () => {
  localStorage.removeItem('user')
  localStorage.removeItem('expireTime')
  localStorage.removeItem('access_token')
  router.push('/login')
}




</script>


<style>
/* 设置全局字体样式 */
body {
  font-family: 'STDongGuanTi Bld';
  font-weight: '400';

  box-sizing: border-box;
}

/* 设置mainContainer的样式背景色 */
.mainContainer {
  background-color: #e4e4e4;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.nabigationBar {
  display: flex;
  background-color: rgb(247, 247, 247);
  margin: 4px 4px;
  border-radius: 4px;

  position: relative;
}

/* ********************************************* 导航栏-左 */

.nabigationBar .left {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.nabigationBar .left img {
  width: 25px;
  height: 25px;
  /* 通过margin设定图片上下左右的距离 */
  margin: 7px 9px;
  /* 设置图片模糊程度 */
  /* filter: blur(8px); */
}

.nabigationBar .left h2 {
  font-size: 15px;
  font-weight: bold;
}

/* ********************************************* 导航栏-右 */

.nabigationBar .right {
  display: flex;
  align-items: center;

  height: 100%;

  position: absolute;
  right: 0;

  /* background-color: blue; */

  padding: 0 19px;
}

.nabigationBar .right h4 {
  font-size: 14px;
}

.nabigationBar .right h4:hover {
  cursor: pointer;
}

.nabigationBar .right .el-divider {
  border-color: #999;
}

.nabigationBar .right .logout-Btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.nabigationBar .logout-Btn:hover {
  cursor: pointer;
}

/* ********************************************* 主内容区 */
.mainContent {
  display: flex;
  flex-direction: row;
  height: 100vh;
}

.mainContent-right {
  display: flex;
  flex-direction: column;
  margin: 4px 7px;
  /* background-color: green; */
}

.functionPad {
  display: flex;
  flex-direction: row;
}
</style>