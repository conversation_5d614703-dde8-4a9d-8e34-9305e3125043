{"version": 3, "file": "js/548.86d5f086.js", "mappings": "0lVAoJA,MAAMA,GAAcC,EAAAA,EAAAA,IAAI,CAAC,GACnBC,GAAsBD,EAAAA,EAAAA,IAAI,MAG1BE,GAAgBF,EAAAA,EAAAA,IAAI,IACpBG,GAAgBH,EAAAA,EAAAA,IAAI,IAGpBI,EAAkBC,IACjBN,EAAYO,MAAMD,KACrBN,EAAYO,MAAMD,GAAS,CACzBE,WAAW,EACXC,SAAS,EACTC,SAAU,EACVC,YAAa,GAEjB,EAIIC,EAAoBN,IACxB,MAAMO,EAAQb,EAAYO,MAAMD,GAChC,OAAIO,GAAOJ,QAAgBK,EAAAA,QACpBD,GAAOL,UAAYO,EAAAA,WAAaC,EAAAA,SAAQ,EAI3CC,EAAuBC,IAC3BC,QAAQC,IAAI,SAAUF,EAAM,EAIxBG,EAAmBf,IACvBN,EAAYO,MAAMD,GAAOE,WAAY,EACrCN,EAAoBK,MAAQD,EAC5Ba,QAAQC,IAAI,UAAWd,EAAM,EAIzBgB,EAAoBhB,IACxBN,EAAYO,MAAMD,GAAOE,WAAY,EACjCN,EAAoBK,QAAUD,IAChCJ,EAAoBK,MAAQ,MAE9BY,QAAQC,IAAI,QAASd,EAAM,EAIvBiB,EAAoBjB,IACxBN,EAAYO,MAAMD,GAAOE,WAAY,EACrCN,EAAoBK,MAAQ,KAC5BP,EAAYO,MAAMD,GAAOK,YAAc,EACvCQ,QAAQC,IAAI,UAAWd,EAAM,EAIzBkB,EAAoBlB,IACxBa,QAAQM,MAAM,UAAWnB,GACzBN,EAAYO,MAAMD,GAAOG,SAAU,EACnCT,EAAYO,MAAMD,GAAOE,WAAY,EACrCkB,EAAAA,GAAUD,MAAM,SAAS,EAIrBE,EAAqBrB,IACzBN,EAAYO,MAAMD,GAAOG,SAAU,EACnCU,QAAQC,IAAI,UAAWd,EAAM,EAMzBsB,EAAkBC,UACtB,IACExB,EAAeC,GAGf,MAAMwB,EAAyB,iBAAVxB,EACnByB,SAASC,cAAc,0BACvBD,SAASC,cAAc,0BAEzB,IAAKF,EAEH,YADAX,QAAQM,MAAM,WAAYnB,GAK5B,GAAIJ,EAAoBK,OAASL,EAAoBK,QAAUD,EAAO,CACpE,MAAM2B,EAA2C,iBAA9B/B,EAAoBK,MACrCwB,SAASC,cAAc,0BACvBD,SAASC,cAAc,0BACrBC,IAAeA,EAAWC,QAC5BD,EAAWE,OAEf,CAGInC,EAAYO,MAAMD,GAAOE,UAC3BsB,EAAaK,QAEbL,EAAaM,MAEjB,CAAE,MAAOX,GACPN,QAAQM,MAAM,YAAaA,GAC3BzB,EAAYO,MAAMD,GAAOG,SAAU,EACnCiB,EAAAA,GAAUD,MAAM,SAClB,G,OAIFY,EAAAA,EAAAA,KAAU,KAERhC,EAAe,gBACfA,EAAe,gBAGfF,EAAcI,MAAQ,2DACtBH,EAAcG,MAAQ,0DAAyD,KAIjF+B,EAAAA,EAAAA,KAAY,KACVtC,EAAYO,MAAQ,CAAC,EACrBL,EAAoBK,MAAQ,IAAG,I,srGCzQjC,MAAMgC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://wccsfront/./src/components/VoiceMessageTest.vue", "webpack://wccsfront/./src/components/VoiceMessageTest.vue?602f"], "sourcesContent": ["<template>\n  <div class=\"voice-test-container\">\n    <h2>语音消息播放测试</h2>\n    \n    <!-- 模拟语音消息 -->\n    <div class=\"message-container received\">\n      <div class=\"avatar\">\n        <img src=\"../assets/人员头像.png\" alt=\"头像\">\n      </div>\n      \n      <div class=\"message-content\">\n        <div class=\"sender-info\">\n          <span class=\"sender-name\">测试用户</span>\n          <span class=\"sender-lable wechat\">@微信</span>\n          <span class=\"message-time\">2024-01-01 12:00:00</span>\n        </div>\n        \n        <!-- 语音消息模板 -->\n        <div class=\"message-bubble voice-content\">\n          <div class=\"voice-wrapper\">\n            <div class=\"voice-controls\">\n              <el-button \n                :icon=\"getVoicePlayIcon('test-voice-1')\" \n                circle \n                size=\"small\"\n                @click=\"toggleVoicePlay('test-voice-1')\"\n                :loading=\"voiceStates['test-voice-1']?.loading\"\n                class=\"voice-play-btn\">\n              </el-button>\n              <span class=\"voice-duration\">5\"</span>\n            </div>\n            <div class=\"voice-visual-container\">\n              <AvWaveform\n                v-if=\"testAudioUrl1\"\n                :audio-src=\"testAudioUrl1\"\n                :canv-width=\"280\"\n                :canv-height=\"40\"\n                :played-line-width=\"2\"\n                :noplayed-line-width=\"1\"\n                :played-line-color=\"'#5188ff'\"\n                :noplayed-line-color=\"'#ddd'\"\n                :playtime-clickable=\"true\"\n                :playtime-with-ms=\"false\"\n                ref-link=\"audioRef1\"\n                @click=\"handleWaveformClick\"\n                class=\"voice-waveform\">\n              </AvWaveform>\n              <div v-else class=\"voice-loading\">\n                <span>加载中...</span>\n              </div>\n            </div>\n            <audio\n              ref=\"audioRef1\"\n              :src=\"testAudioUrl1\"\n              @play=\"handleAudioPlay('test-voice-1')\"\n              @pause=\"handleAudioPause('test-voice-1')\"\n              @ended=\"handleAudioEnded('test-voice-1')\"\n              @error=\"handleAudioError('test-voice-1')\"\n              @loadedmetadata=\"handleAudioLoaded('test-voice-1')\"\n              style=\"display: none;\">\n            </audio>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 发送方语音消息 -->\n    <div class=\"message-container sent\">\n      <div class=\"message-content\">\n        <div class=\"sender-info\">\n          <span class=\"sender-name\">我</span>\n          <span class=\"sender-lable employee\">@员工</span>\n          <span class=\"message-time\">2024-01-01 12:01:00</span>\n        </div>\n        \n        <div class=\"message-bubble voice-content\">\n          <div class=\"voice-wrapper\">\n            <div class=\"voice-controls\">\n              <el-button \n                :icon=\"getVoicePlayIcon('test-voice-2')\" \n                circle \n                size=\"small\"\n                @click=\"toggleVoicePlay('test-voice-2')\"\n                :loading=\"voiceStates['test-voice-2']?.loading\"\n                class=\"voice-play-btn\">\n              </el-button>\n              <span class=\"voice-duration\">3\"</span>\n            </div>\n            <div class=\"voice-visual-container\">\n              <AvWaveform\n                v-if=\"testAudioUrl2\"\n                :audio-src=\"testAudioUrl2\"\n                :canv-width=\"280\"\n                :canv-height=\"40\"\n                :played-line-width=\"2\"\n                :noplayed-line-width=\"1\"\n                :played-line-color=\"'#1976d2'\"\n                :noplayed-line-color=\"'#ddd'\"\n                :playtime-clickable=\"true\"\n                :playtime-with-ms=\"false\"\n                ref-link=\"audioRef2\"\n                @click=\"handleWaveformClick\"\n                class=\"voice-waveform\">\n              </AvWaveform>\n              <div v-else class=\"voice-loading\">\n                <span>加载中...</span>\n              </div>\n            </div>\n            <audio\n              ref=\"audioRef2\"\n              :src=\"testAudioUrl2\"\n              @play=\"handleAudioPlay('test-voice-2')\"\n              @pause=\"handleAudioPause('test-voice-2')\"\n              @ended=\"handleAudioEnded('test-voice-2')\"\n              @error=\"handleAudioError('test-voice-2')\"\n              @loadedmetadata=\"handleAudioLoaded('test-voice-2')\"\n              style=\"display: none;\">\n            </audio>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"avatar\">\n        <img src=\"../assets/人员头像.png\" alt=\"头像\">\n      </div>\n    </div>\n\n    <!-- 测试说明 -->\n    <div class=\"test-info\">\n      <h3>测试说明：</h3>\n      <ul>\n        <li>这是一个语音消息播放功能的测试页面</li>\n        <li>使用了WaveSurfer.js来实现音频波形显示和播放控制</li>\n        <li>支持播放/暂停切换</li>\n        <li>显示音频波形可视化</li>\n        <li>响应式设计，适配不同屏幕尺寸</li>\n      </ul>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, onUnmounted } from 'vue'\nimport { VideoPlay, VideoPause, Loading } from '@element-plus/icons-vue'\nimport { ElMessage } from 'element-plus'\nimport { AvWaveform } from 'vue-audio-visual'\n\n// vue-audio-visual 相关状态管理\nconst voiceStates = ref({})\nconst currentPlayingVoice = ref(null)\n\n// 测试音频URL\nconst testAudioUrl1 = ref('')\nconst testAudioUrl2 = ref('')\n\n// 初始化语音播放状态\nconst initVoiceState = (msgid) => {\n  if (!voiceStates.value[msgid]) {\n    voiceStates.value[msgid] = {\n      isPlaying: false,\n      loading: false,\n      duration: 0,\n      currentTime: 0\n    }\n  }\n}\n\n// 获取语音播放按钮图标\nconst getVoicePlayIcon = (msgid) => {\n  const state = voiceStates.value[msgid]\n  if (state?.loading) return Loading\n  return state?.isPlaying ? VideoPause : VideoPlay\n}\n\n// 处理波形点击事件\nconst handleWaveformClick = (event) => {\n  console.log('波形被点击:', event)\n}\n\n// 处理音频播放事件\nconst handleAudioPlay = (msgid) => {\n  voiceStates.value[msgid].isPlaying = true\n  currentPlayingVoice.value = msgid\n  console.log('音频开始播放:', msgid)\n}\n\n// 处理音频暂停事件\nconst handleAudioPause = (msgid) => {\n  voiceStates.value[msgid].isPlaying = false\n  if (currentPlayingVoice.value === msgid) {\n    currentPlayingVoice.value = null\n  }\n  console.log('音频暂停:', msgid)\n}\n\n// 处理音频结束事件\nconst handleAudioEnded = (msgid) => {\n  voiceStates.value[msgid].isPlaying = false\n  currentPlayingVoice.value = null\n  voiceStates.value[msgid].currentTime = 0\n  console.log('音频播放结束:', msgid)\n}\n\n// 处理音频错误事件\nconst handleAudioError = (msgid) => {\n  console.error('音频播放错误:', msgid)\n  voiceStates.value[msgid].loading = false\n  voiceStates.value[msgid].isPlaying = false\n  ElMessage.error('语音播放失败')\n}\n\n// 处理音频加载完成事件\nconst handleAudioLoaded = (msgid) => {\n  voiceStates.value[msgid].loading = false\n  console.log('音频加载完成:', msgid)\n}\n\n\n\n// 切换语音播放状态\nconst toggleVoicePlay = async (msgid) => {\n  try {\n    initVoiceState(msgid)\n\n    // 获取对应的audio元素\n    const audioElement = msgid === 'test-voice-1' ?\n      document.querySelector('audio[ref=\"audioRef1\"]') :\n      document.querySelector('audio[ref=\"audioRef2\"]')\n\n    if (!audioElement) {\n      console.error('音频元素未找到:', msgid)\n      return\n    }\n\n    // 暂停其他正在播放的语音\n    if (currentPlayingVoice.value && currentPlayingVoice.value !== msgid) {\n      const otherAudio = currentPlayingVoice.value === 'test-voice-1' ?\n        document.querySelector('audio[ref=\"audioRef1\"]') :\n        document.querySelector('audio[ref=\"audioRef2\"]')\n      if (otherAudio && !otherAudio.paused) {\n        otherAudio.pause()\n      }\n    }\n\n    // 切换播放状态\n    if (voiceStates.value[msgid].isPlaying) {\n      audioElement.pause()\n    } else {\n      audioElement.play()\n    }\n  } catch (error) {\n    console.error('语音播放切换失败:', error)\n    voiceStates.value[msgid].loading = false\n    ElMessage.error('语音播放失败')\n  }\n}\n\n// 组件挂载时初始化\nonMounted(() => {\n  // 初始化测试语音状态\n  initVoiceState('test-voice-1')\n  initVoiceState('test-voice-2')\n\n  // 设置测试音频URL（使用在线测试音频）\n  testAudioUrl1.value = 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'\n  testAudioUrl2.value = 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'\n})\n\n// 组件卸载时清理资源\nonUnmounted(() => {\n  voiceStates.value = {}\n  currentPlayingVoice.value = null\n})\n</script>\n\n<style scoped>\n.voice-test-container {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n.voice-test-container h2 {\n  text-align: center;\n  color: #303133;\n  margin-bottom: 30px;\n}\n\n/* 消息容器样式 */\n.message-container {\n  display: flex;\n  margin-bottom: 20px;\n  align-items: flex-start;\n  gap: 12px;\n}\n\n.message-container.sent {\n  flex-direction: row-reverse;\n}\n\n.avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  overflow: hidden;\n  flex-shrink: 0;\n}\n\n.avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.message-content {\n  max-width: 70%;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.sender-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 12px;\n  color: #909399;\n}\n\n.sender-name {\n  font-weight: 500;\n  color: #303133;\n}\n\n.sender-lable {\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 500;\n}\n\n.sender-lable.wechat {\n  background-color: #e8f5e8;\n  color: #67c23a;\n}\n\n.sender-lable.employee {\n  background-color: #e3f2fd;\n  color: #409eff;\n}\n\n.message-bubble {\n  padding: 12px 16px;\n  border-radius: 12px;\n  background-color: #ffffff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  border: 1px solid #e4e7ed;\n}\n\n.sent .message-bubble {\n  background-color: #e3f2fd;\n  border-color: #bbdefb;\n}\n\n/* 语音消息样式 */\n.voice-content {\n  min-width: 200px;\n  max-width: 350px;\n  transition: all 0.3s ease;\n}\n\n.voice-content:hover {\n  background-color: #f5f7fa;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.sent .voice-content:hover {\n  background-color: #d6eafb;\n}\n\n.voice-wrapper {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.voice-controls {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.voice-play-btn {\n  width: 36px;\n  height: 36px;\n  border: none;\n  background-color: #5188ff;\n  color: white;\n  transition: all 0.3s ease;\n}\n\n.voice-play-btn:hover {\n  background-color: #4070ff;\n  transform: scale(1.05);\n}\n\n.voice-play-btn:active {\n  transform: scale(0.95);\n}\n\n.sent .voice-play-btn {\n  background-color: #1976d2;\n}\n\n.sent .voice-play-btn:hover {\n  background-color: #1565c0;\n}\n\n.voice-duration {\n  color: #606266;\n  font-size: 14px;\n  font-weight: 500;\n  margin-left: auto;\n}\n\n.voice-visual-container {\n  width: 100%;\n  height: 40px;\n  position: relative;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.sent .voice-visual-container {\n  background-color: #e8f4fd;\n}\n\n.voice-waveform {\n  width: 100%;\n  height: 100%;\n}\n\n.voice-loading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #f0f0f0;\n  width: 100%;\n  height: 100%;\n  color: #999;\n  font-size: 12px;\n}\n\n/* 音频播放时的动画效果 */\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n.voice-content.playing .voice-play-btn {\n  animation: pulse 1.5s infinite;\n}\n\n/* 测试说明样式 */\n.test-info {\n  margin-top: 40px;\n  padding: 20px;\n  background-color: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.test-info h3 {\n  color: #303133;\n  margin-bottom: 15px;\n}\n\n.test-info ul {\n  color: #606266;\n  line-height: 1.6;\n}\n\n.test-info li {\n  margin-bottom: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .voice-test-container {\n    padding: 15px;\n  }\n\n  .message-content {\n    max-width: 85%;\n  }\n\n  .voice-content {\n    min-width: 180px;\n    max-width: 280px;\n    padding: 10px 12px;\n  }\n\n  .voice-play-btn {\n    width: 32px;\n    height: 32px;\n  }\n\n  .voice-visual-container {\n    height: 35px;\n  }\n}\n</style>\n", "import script from \"./VoiceMessageTest.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./VoiceMessageTest.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./VoiceMessageTest.vue?vue&type=style&index=0&id=01e88d1f&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-01e88d1f\"]])\n\nexport default __exports__"], "names": ["voiceStates", "ref", "currentPlayingVoice", "testAudioUrl1", "testAudioUrl2", "initVoiceState", "msgid", "value", "isPlaying", "loading", "duration", "currentTime", "getVoicePlayIcon", "state", "Loading", "VideoPause", "VideoPlay", "handleWaveformClick", "event", "console", "log", "handleAudioPlay", "handleAudioPause", "handleAudioEnded", "handleAudioError", "error", "ElMessage", "handleAudioLoaded", "toggleVoicePlay", "async", "audioElement", "document", "querySelector", "otherAudio", "paused", "pause", "play", "onMounted", "onUnmounted", "__exports__"], "sourceRoot": ""}