<!-- TODO：
固定搜索框的宽度 -->
<template>
    <div class="mainContainer-grouplist">
        <div class="search" style="width: 800px;">
            <el-input placeholder="搜索群聊" v-model="searchWord" clearable class="search-input">
                <template #prefix>
                    <el-icon class="search-icon">
                        <Search />
                    </el-icon>
                </template>
            </el-input>
        </div>

        <div class="lable-navigator">
            <el-tabs v-model="activeName" class="grouplist-tabs" @tab-click="handleClick">
                <el-tab-pane label="全部" name="first">
                    <div class="group-card" v-for="group in grouplist" :key="group.roomid"
                        @click="handleGroupClick(group)">
                        <div class="profile">
                            <img class="staff-avatar" src="@/assets/群聊头像.png" alt="群聊头像">
                        </div>
                        <div class="info">
                            <p class="name">{{ getGroupName(group) }}</p>
                            <div class="info-detail">
                                <p class="totalNum">{{ goup_chatId_cache[group.chatId] || '加载中...' }}人 | </p>
                                <p class="owner">群主：{{ ownerNames[group.owner] || '加载中...' }}</p>
                            </div>
                        </div>

                    </div>
                </el-tab-pane>
                <el-tab-pane label="内部" name="second">
                    <div class="group-card" v-for="group in grouplist" :key="group.roomid"
                        @click="handleGroupClick(group)">
                        <div class="profile">
                            <img class="staff-avatar" src="@/assets/群聊头像.png" alt="群聊头像">
                        </div>
                        <div class="info">
                            <p class="name">{{ getGroupName(group) }}</p>
                            <div class="info-detail">
                                <p class="totalNum">{{ goup_chatId_cache[group.chatId] || '加载中...' }}人 | </p>
                                <p class="owner">群主：{{ ownerNames[group.owner] || '加载中...' }}</p>
                            </div>
                        </div>

                    </div>
                </el-tab-pane>
                <el-tab-pane label="外部" name="third">
                    <div class="group-card" v-for="group in grouplist" :key="group.roomid"
                        @click="handleGroupClick(group)">
                        <div class="profile">
                            <img class="staff-avatar" src="@/assets/群聊头像.png" alt="群聊头像">
                        </div>
                        <div class="info">
                            <p class="name">{{ getGroupName(group) }}</p>
                            <div class="info-detail">
                                <p class="totalNum">{{ goup_chatId_cache[group.chatId] || '加载中...' }}人 | </p>
                                <p class="owner">群主：{{ ownerNames[group.owner] || '加载中...' }}</p>
                            </div>
                        </div>

                    </div>
                </el-tab-pane>
                <!-- <el-tab-pane label="Config" name="second">Config</el-tab-pane>
                <el-tab-pane label="Role" name="third">Role</el-tab-pane>
                <el-tab-pane label="Task" name="fourth">Task</el-tab-pane> -->
            </el-tabs>
        </div>

        <div class="pageContainer">
            <el-pagination v-model:current-page="currentPage" background :size="size" layout="total, prev, pager, next"
                :total="totalGroups" :page-size="pageSize" :pager-count="3" small @current-change="handlePageChange">
            </el-pagination>
        </div>
    </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue';
import { ref, onMounted, watch, inject } from 'vue';
import axiosInstance from '@/axiosConfig.js'; // 引入 axios 实例
import { ElMessage } from 'element-plus';

const searchWord = ref(''); // searchWord 控制搜索框
const activeName = ref('first') // activeName 控制 tab 切换

const grouplist = ref([]); // grouplist 群聊列表-全部
const grouplist_inner = ref([]); // grouplist 群聊列表-内部
const grouplist_outter = ref([]); // grouplist 群聊列表-外部
const totalGroups = ref('');//grouplist 人员总数

const currentPage = ref(1); // currentPage 当前页码
const pageSize = ref(100); // pageSize 每页显示数量

const currentComponent_List = inject('currentComponent_List')
const currentComponent_ChatList = inject('currentComponent_ChatList')

// -------------------------------------------------------------------------- 获取存档人员列表与搜索实现
const getgrouplist = (curPage, maxPageNum, searchName) => {
    currentComponent_List.value = 'GroupList'
    currentComponent_ChatList.value = ''

    const jwt_token = localStorage.getItem('access_token')

    axiosInstance.post('/api/chatmessage/chatter', {
        type: "groupchat",
        page: curPage,
        limit: maxPageNum,
        name: searchName
    }, {
        headers: {
            Authorization: 'Bearer ' + jwt_token
        },
    },).then(res => {
        if (res.data.code === 0) {
            console.log('获取群聊列表成功', res.data.data.result)
            grouplist.value = res.data.data.result;
            totalGroups.value = res.data.data.total;

            // 根据chatType分类群聊
            grouplist_inner.value = grouplist.value.filter(group => !group.chatType);
            grouplist_outter.value = grouplist.value.filter(group => group.chatType === 'customer');

            // 获取每个群聊的群主名称
            grouplist.value.forEach(group => {
                if (group.owner) {
                    getOwnerName(group.owner);
                }
                if (group.chatId) {
                    getGroupMenberNumber(group.chatId)
                }
            });
        }
    }).catch(error => {
        console.log(error);
        ElMessage.error('获取群聊列表失败，请检查网络或联系管理员');
    });
}

//页码变化时触发---限定一次性获取全部群聊，一般只有一页
const handlePageChange = () => {
    console.log('')
}
//搜索框变化时触发
const filterEmployees = () => {
    if (searchWord.value) {
        currentPage.value = 1; // 重置为第一页
    }
    // 在所有群聊中搜索
    const allGroups = [...grouplist_inner.value, ...grouplist_outter.value];
    const filteredGroups = allGroups.filter(group =>
        group.name.toLowerCase().includes(searchWord.value.toLowerCase())
    );

    // 根据当前选中的标签显示对应的群聊
    switch (activeName.value) {
        case 'first': // 全部
            grouplist.value = filteredGroups;
            totalGroups.value = grouplist.value.length
            break;
        case 'second': // 内部
            grouplist.value = filteredGroups.filter(group => !group.chatType);
            totalGroups.value = grouplist.value.length
            break;
        case 'third': // 外部
            grouplist.value = filteredGroups.filter(group => group.chatType === 'customer');
            totalGroups.value = grouplist.value.length
            break;
    }
}


onMounted(() => getgrouplist(1, 100, ''));
watch(searchWord, filterEmployees);
//-------------------------------------------------------------------------- 点击导航标签
const handleClick = (tab) => {
    console.log("检测到群聊标签被点击", tab.props.name);
    // 根据标签名称切换显示不同的群聊列表
    switch (tab.props.name) {
        case 'first': // 全部
            grouplist.value = [...grouplist_inner.value, ...grouplist_outter.value];
            totalGroups.value = grouplist.value.length
            break;
        case 'second': // 内部
            grouplist.value = grouplist_inner.value;
            totalGroups.value = grouplist.value.length
            break;
        case 'third': // 外部
            grouplist.value = grouplist_outter.value;
            totalGroups.value = grouplist.value.length
            break;
    }
}
// -------------------------------------------------------------------------- 点击群聊卡片
//注入选中的会话
const selectedChat = inject('selectedChat', null);

// 处理员工卡片点击事件
const handleGroupClick = (group) => {
    let from = group.chatId //会话发起方id，如果 type = groupchat，为群聊ID；
    let to = '' //会话接收方id，如果 type = groupchat，为 null；

    let fromName = getGroupName(group) //会话发起方名称，如果 type = groupchat，为群聊名称；
    let fromLable = '' //会话发起方标签，如果 type = groupchat，为null；
    let fromAvatar = ''

    let toName = '' //会话接收方名称，如果 type = groupchat，为 null；
    let toAvatar = '' //会话接收方头像 ，如果 type = groupchat，为 null；
    let toLable = '' //会话接收方类型 ，如果 type = groupchat，为 null,为employee时是员工的标签，默认是‘员工’；

    let type = 'groupchat' //会话接收方类型，群聊 type = groupchat，
    let page = 1 //拉取第一页数据
    let limit = 100 //拉取30条会话

    const selectedChat_info = {
        from: from,
        to: to,

        fromName: fromName,
        fromLable: fromLable,
        fromAvatar: fromAvatar,

        toName: toName,
        toAvatar: toAvatar,
        toLable: toLable,

        type: type,//groupchat（群聊），其它一律视为非群聊
        page: page,
        limit: limit
    }
    selectedChat.value = selectedChat_info,

        console.log('到某一群聊会话被点击', selectedChat.value);

};



// --------------------------------------------------------------------------控制群名显示

const getGroupName = (group) => {
    if (group.name) {
        return group.name
    }
    const groupId = group.chatId
    const groupName = '群聊'
    if (groupId && groupId.length > 6) {
        group.name = groupName + groupId.slice(0, 9)+ '...'
        return groupName + groupId.slice(0, 9) + '...'
    }
    return ''
}

// --------------------------------------------------------------------------基于ownerId获取ownerName
// 键值存储已获取的人员的 id, 避免接口冗余调用
const owner_nameAndLable = ref({});
const owner_nameAndLable_cache = ref({});
// 记录正在请求中的用户ID
const pending_requests = ref(new Set());

// 添加一个ref来存储群主名称
const ownerNames = ref({})

const getOwnerName = async (from_userId) => {
    // 如果已经有缓存的名称，直接返回
    if (ownerNames.value[from_userId]) {
        return ownerNames.value[from_userId];
    }

    // 如果已经在获取中或已有缓存，则不重复获取
    if (pending_requests.value.has(from_userId) || owner_nameAndLable_cache.value[from_userId]) {
        const cachedData = owner_nameAndLable_cache.value[from_userId];
        ownerNames.value[from_userId] = cachedData?.from_user.name || '未知用户';
        return ownerNames.value[from_userId];
    }

    try {
        // 添加到正在请求的集合中
        pending_requests.value.add(from_userId);

        console.log('调用getOwnerName方法')
        const jwt_token = localStorage.getItem('access_token');
        const response = await axiosInstance.get('/api/chatmessage/detail/getNameAndLable', {
            params: { from_userId },
            headers: { Authorization: 'Bearer ' + jwt_token }
        });
        const data = response.data.data;
        owner_nameAndLable.value[from_userId] = data;
        owner_nameAndLable_cache.value[from_userId] = data;
        ownerNames.value[from_userId] = data.from_user.name;
        return ownerNames.value[from_userId];
    } catch (error) {
        console.error('获取群聊owner信息失败:', error);
        owner_nameAndLable.value[from_userId] = { from_user: { name: '未知用户' } };
        owner_nameAndLable_cache.value[from_userId] = { from_user: { name: '未知用户' } };
        ownerNames.value[from_userId] = '未知用户';
        return '未知用户';
    } finally {
        // 请求完成后从集合中移除
        pending_requests.value.delete(from_userId);
    }
};

// 键值存储已获取的人员的 id, 避免接口冗余调用
const goup_chatId = ref({});
const goup_chatId_cache = ref({});
// 记录正在请求中的用户ID
const pending_requests1 = ref(new Set());


const getGroupMenberNumber = async (chatId) => {

    // 如果已经在获取中或已有缓存，则不重复获取
    if (pending_requests1.value.has(chatId)) {
        return;
    }
    if (goup_chatId_cache.value[chatId]) {
        const cachedData = goup_chatId_cache.value[chatId];
        goup_chatId.value[chatId] = cachedData || 0;
        return cachedData;
    }

    try {
        // 添加到正在请求的集合中
        pending_requests.value.add(chatId);

        console.log('调用getGroupMenberNumber方法')

        const jwt_token = localStorage.getItem('access_token');

        const response = await axiosInstance.get('/api/chatmessage/chatter/getgroupMenberCount', {
            params: { chatId },
            headers: { Authorization: 'Bearer ' + jwt_token }
        });
        const data = response.data.data;
        goup_chatId.value[chatId] = data;
        goup_chatId_cache.value[chatId] = data;
        goup_chatId.value[chatId] = data;
        return goup_chatId.value[chatId];
    } catch (error) {
        console.error('获取群聊人数失败:', error);
        goup_chatId.value[chatId] = 0;
        goup_chatId_cache.value[chatId] = 0;
        return 0;
    } finally {
        // 请求完成后从集合中移除
        pending_requests1.value.delete(chatId);
    }

}

</script>

<style>
.mainContainer-grouplist {
    margin: 0;
    height: calc(100vh - 6.969rem);
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    width: 15.625rem;
    padding: 1rem 0.75rem;
    border-right: 0.0625rem solid #e6e6e6;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.02);
}

.search {
    margin-bottom: 0.3125rem;
}

.search-input {
    transition: all 0.3s ease;
}

.search-input:hover .el-input__wrapper {
    box-shadow: 0 0 0 0.0625rem #e6e6e6 inset;
}

.search-icon {
    color: #909399;
}

.lable-navigator {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.lable-navigator::-webkit-scrollbar {
    width: 0.375rem;
}

.lable-navigator::-webkit-scrollbar-thumb {
    background-color: #e0e0e0;
    border-radius: 0.1875rem;
}

.lable-navigator::-webkit-scrollbar-track {
    background-color: transparent;
}

.group-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 4rem;
    padding: 0 0.75rem;
    margin: 0.25rem 0;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.group-card:hover {
    background-color: #f5f7fa;
    transform: translateX(0.125rem);
}

.group-card .profile {
    /* background: linear-gradient(135deg, #1890ff, #0960bd); */
    /* color: #ffffff; */
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5rem;
    margin-right: 0.75rem;
    /* font-weight: 500; */
    box-shadow: 0 0.125rem 0.25rem rgba(24, 144, 255, 0.1);
}

.group-card .profile img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.group-card .info {
    flex: 1;
}

.group-card .info .name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.group-card .info .info-detail {
    font-size: 0.83rem;
    font-weight: 500;
    color: #5f6974;

    display: flex;
    flex-direction: row;
}

.group-card .info .info-detail .owner {
    margin-left: 0.3rem;
}

.pageContainer {
    display: flex;
    justify-content: center;
    padding: 0.75rem 0;
    margin-top: 0.5rem;
    border-top: 0.0625rem solid #f0f0f0;
    width: 100%;
}

.pageContainer :deep(.el-pagination) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 0 0.5rem;
    font-size: 0.8125rem;
}

.pageContainer :deep(.el-pagination .el-pagination__total) {
    min-width: auto;
    margin-right: 0.5rem;
    font-size: 0.8125rem;
    color: #606266;
}

.pageContainer :deep(.el-pagination .el-pager) {
    margin: 0;
}

.pageContainer :deep(.el-pagination .el-pager li) {
    min-width: 1.5rem;
    height: 1.5rem;
    line-height: 1.5rem;
    font-weight: normal;
    margin: 0 0.125rem;
}

.pageContainer :deep(.el-pagination .el-pager li.active) {
    background-color: #1890ff;
    color: #ffffff;
}

.pageContainer :deep(.el-pagination button) {
    min-width: 1.5rem;
    height: 1.5rem;
    margin: 0;
    padding: 0;
}

.pageContainer :deep(.el-pagination .btn-prev),
.pageContainer :deep(.el-pagination .btn-next) {
    margin: 0 0.125rem;
    background-color: #f4f4f5;
}

.pageContainer :deep(.el-pagination .el-icon) {
    font-size: 0.75rem;
}

.pageContainer :deep(.el-pagination .more) {
    background-color: transparent;
}

.grouplist-tabs {
    height: 100%;
}

.grouplist-tabs :deep(.el-tabs__header) {
    margin-bottom: 1rem;
}

.grouplist-tabs :deep(.el-tabs__nav-wrap::after) {
    height: 0.0625rem;
    background-color: #f0f0f0;
}

.grouplist-tabs :deep(.el-tabs__item) {
    font-size: 0.875rem;
    color: #606266;
    padding: 0 1rem;
}

.grouplist-tabs :deep(.el-tabs__item.is-active) {
    color: #1890ff;
    font-weight: 500;
}

.grouplist-tabs :deep(.el-tabs__active-bar) {
    background-color: #1890ff;
    height: 0.125rem;
}
</style>
