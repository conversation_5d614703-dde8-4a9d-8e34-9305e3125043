<!-- TODO：
固定搜索框的宽度 -->
<template>
    <div class="mainContainer-employeelist">
        <div class="search" style="width: 800px;">
            <el-input placeholder="搜索存档人员" v-model="searchWord" clearable class="search-input">
                <template #prefix>
                    <el-icon class="search-icon">
                        <Search />
                    </el-icon>
                </template>
            </el-input>
        </div>

        <div class="lable-navigator">
            <el-tabs v-model="activeName" class="employeeList-tabs" @tab-click="handleClick">
                <el-tab-pane label="存档人员" name="first">
                    <div class="employee-card" v-for="employee in archivedEmployees" :key="employee.name"
                        @click="handleEmployeeClick(employee)">
                        <div class="profile">
                            <img class="staff-avatar" src="@/assets/人员头像.png" alt="人员头像">
                        </div>
                        <div class="info">
                            <p class="name">{{ employee.name }}</p>
                            <p class="lable">存档员工</p>
                        </div>
                        <div class="mark">
                            <el-tag size="small" type="primary" effect="plain">存档</el-tag>
                        </div>
                    </div>
                </el-tab-pane>
                <!-- <el-tab-pane label="Config" name="second">Config</el-tab-pane>
                <el-tab-pane label="Role" name="third">Role</el-tab-pane>
                <el-tab-pane label="Task" name="fourth">Task</el-tab-pane> -->
            </el-tabs>
        </div>

        <div class="pageContainer">
            <el-pagination v-model:current-page="currentPage" background :size="size" layout="total, prev, pager, next"
                :total="totalEmployees" :page-size="pageSize" :pager-count="3" small @current-change="handlePageChange">
            </el-pagination>
        </div>
    </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue';
import { ref, onMounted, watch, inject, computed } from 'vue';
import axiosInstance from '@/axiosConfig.js'; 
import { ElMessage } from 'element-plus';

const searchWord = ref(''); // searchWord 响应搜索框输入数据
const activeName = ref('first') // activeName 响应导航栏的tab切换，默认切换第一个tab

const employeeList = ref([]); // employeeList 员工列表
const totalEmployees = ref('');//employeeList 分页组件展示的Total值

const currentPage = ref(1); // currentPage 响应当前页码，默认第一页
const pageSize = ref(100); // pageSize 响应每页数量，默认100条

const currentComponent_List = inject('currentComponent_List')
const currentComponent_ChatList = inject('currentComponent_ChatList')

// -------------------------------------------------------------------------- 获取员工列表与搜索实现
const getEmployeeList = (curPage, maxPageNum, searchName) => {

    currentComponent_List.value = 'EmployeeList';
    currentComponent_ChatList.value = 'EmployeeChatList';

    // 获取存档人员列表
    const jwt_token = localStorage.getItem('access_token')

    console.log('获取员工列表接口调用')

    axiosInstance.post('/api/chatmessage/chatter', {
        type: "employee", 
        page: curPage,
        limit: maxPageNum,
        name: searchName
    }, {
        headers: {
            Authorization: 'Bearer ' + jwt_token
        },
    },).then(res => {

        if (res.data.code === 0) {
            console.log('成功获取员工列表', res.data.data.result)
            employeeList.value = res.data.data.result;
            //此处的数量是所获取的人员列表的数量，非存档员工列表数量
            totalEmployees.value = res.data.data.total;
        }


    }).catch(error => {
        console.log(error);
        ElMessage.error('获取存档人员列表失败，请检查网络或联系管理员');
    });
}

//搜索框变化时调用
const filterEmployees = () => {
    if (searchWord.value) {
        currentPage.value = 1; 
    }
    getEmployeeList(currentPage.value, pageSize.value, searchWord.value);
}

//页码变化时调用
const handlePageChange = (page) => {
    currentPage.value = page;
    getEmployeeList(currentPage.value, pageSize.value, searchWord.value);
}

onMounted(() => getEmployeeList(1, 100, ''));
watch(searchWord, filterEmployees);


// -------------------------------------------------------------------------- 计算属性：仅展示存档人员
const archivedEmployees = computed(() => {
    console.log('已过滤存档员工')
    return employeeList.value.filter(employee => employee.sessionArchiveFlag === 1);
});

// 更新总数计算
watch(archivedEmployees, (newVal) => {
    console.log('存档员工总数', newVal.length)
    totalEmployees.value = newVal.length;
});









// -------------------------------------------------------------------------- 点击员工卡片，传递员工信息（名称，头像）至 EmployeeChatList 组件
const selectedEmployee = inject('selectedEmployee', ref(null));

// 员工卡片点击时调用
const handleEmployeeClick = (employee) => {
    // 记录选中的员工信息，没有选中时为ref(null)
    if (selectedEmployee) {
        selectedEmployee.value = {
            name: employee.name,
            avatar: require('@/assets/人员头像.png'),
            label: '存档员工',
            id: employee.userid || '',
        };
    }
    console.log('已选中员工:', employee.name);
};

const handleClick = (tab, event) => {
    console.log(tab, event)
}



</script>

<style>
.mainContainer-employeelist {
    margin: 0;
    height: calc(100vh - 6.969rem);
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    width: 15.625rem;
    padding: 1rem 0.75rem;
    border-right: 0.0625rem solid #e6e6e6;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.02);
}

.search {
    margin-bottom: 0.3125rem;
}

.search-input {
    transition: all 0.3s ease;
}

.search-input:hover .el-input__wrapper {
    box-shadow: 0 0 0 0.0625rem #e6e6e6 inset;
}

.search-icon {
    color: #909399;
}

.lable-navigator {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.lable-navigator::-webkit-scrollbar {
    width: 0.375rem;
}

.lable-navigator::-webkit-scrollbar-thumb {
    background-color: #e0e0e0;
    border-radius: 0.1875rem;
}

.lable-navigator::-webkit-scrollbar-track {
    background-color: transparent;
}

.employee-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 4rem;
    padding: 0 0.75rem;
    margin: 0.25rem 0;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.employee-card:hover {
    background-color: #f5f7fa;
    transform: translateX(0.125rem);
}

.employee-card .profile {
    /* background: linear-gradient(135deg, #1890ff, #0960bd); */
    /* color: #ffffff; */
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5rem;
    margin-right: 0.75rem;
    /* font-weight: 500; */
    box-shadow: 0 0.125rem 0.25rem rgba(24, 144, 255, 0.1);
}

.employee-card .profile img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.employee-card .info {
    flex: 1;
}

.employee-card .info .name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.employee-card .info .lable {
    font-size: 0.75rem;
    color: #909399;
}

.employee-card .mark {
    margin-left: 0.5rem;
}

.pageContainer {
    display: flex;
    justify-content: center;
    padding: 0.75rem 0;
    margin-top: 0.5rem;
    border-top: 0.0625rem solid #f0f0f0;
    width: 100%;
}

.pageContainer :deep(.el-pagination) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 0 0.5rem;
    font-size: 0.8125rem;
}

.pageContainer :deep(.el-pagination .el-pagination__total) {
    min-width: auto;
    margin-right: 0.5rem;
    font-size: 0.8125rem;
    color: #606266;
}

.pageContainer :deep(.el-pagination .el-pager) {
    margin: 0;
}

.pageContainer :deep(.el-pagination .el-pager li) {
    min-width: 1.5rem;
    height: 1.5rem;
    line-height: 1.5rem;
    font-weight: normal;
    margin: 0 0.125rem;
}

.pageContainer :deep(.el-pagination .el-pager li.active) {
    background-color: #1890ff;
    color: #ffffff;
}

.pageContainer :deep(.el-pagination button) {
    min-width: 1.5rem;
    height: 1.5rem;
    margin: 0;
    padding: 0;
}

.pageContainer :deep(.el-pagination .btn-prev),
.pageContainer :deep(.el-pagination .btn-next) {
    margin: 0 0.125rem;
    background-color: #f4f4f5;
}

.pageContainer :deep(.el-pagination .el-icon) {
    font-size: 0.75rem;
}

.pageContainer :deep(.el-pagination .more) {
    background-color: transparent;
}

.employeeList-tabs {
    height: 100%;
}

.employeeList-tabs :deep(.el-tabs__header) {
    margin-bottom: 1rem;
}

.employeeList-tabs :deep(.el-tabs__nav-wrap::after) {
    height: 0.0625rem;
    background-color: #f0f0f0;
}

.employeeList-tabs :deep(.el-tabs__item) {
    font-size: 0.875rem;
    color: #606266;
    padding: 0 1rem;
}

.employeeList-tabs :deep(.el-tabs__item.is-active) {
    color: #1890ff;
    font-weight: 500;
}

.employeeList-tabs :deep(.el-tabs__active-bar) {
    background-color: #1890ff;
    height: 0.125rem;
}
</style>
