<template>
  <div class="voice-test-container">
    <h2>语音消息播放测试</h2>
    
    <!-- 模拟语音消息 -->
    <div class="message-container received">
      <div class="avatar">
        <img src="../assets/人员头像.png" alt="头像">
      </div>
      
      <div class="message-content">
        <div class="sender-info">
          <span class="sender-name">测试用户</span>
          <span class="sender-lable wechat">@微信</span>
          <span class="message-time">2024-01-01 12:00:00</span>
        </div>
        
        <!-- 语音消息模板 -->
        <div class="message-bubble voice-content">
          <div class="voice-wrapper">
            <div class="voice-controls">
              <el-button 
                :icon="getVoicePlayIcon('test-voice-1')" 
                circle 
                size="small"
                @click="toggleVoicePlay('test-voice-1')"
                :loading="voiceStates['test-voice-1']?.loading"
                class="voice-play-btn">
              </el-button>
              <span class="voice-duration">5"</span>
            </div>
            <div class="voice-visual-container">
              <AvWaveform
                v-if="testAudioUrl1"
                :audio-src="testAudioUrl1"
                :canv-width="280"
                :canv-height="40"
                :played-line-width="2"
                :noplayed-line-width="1"
                :played-line-color="'#5188ff'"
                :noplayed-line-color="'#ddd'"
                :playtime-clickable="true"
                :playtime-with-ms="false"
                ref-link="audioRef1"
                @click="handleWaveformClick"
                class="voice-waveform">
              </AvWaveform>
              <div v-else class="voice-loading">
                <span>加载中...</span>
              </div>
            </div>
            <audio
              ref="audioRef1"
              :src="testAudioUrl1"
              @play="handleAudioPlay('test-voice-1')"
              @pause="handleAudioPause('test-voice-1')"
              @ended="handleAudioEnded('test-voice-1')"
              @error="handleAudioError('test-voice-1')"
              @loadedmetadata="handleAudioLoaded('test-voice-1')"
              style="display: none;">
            </audio>
          </div>
        </div>
      </div>
    </div>

    <!-- 发送方语音消息 -->
    <div class="message-container sent">
      <div class="message-content">
        <div class="sender-info">
          <span class="sender-name">我</span>
          <span class="sender-lable employee">@员工</span>
          <span class="message-time">2024-01-01 12:01:00</span>
        </div>
        
        <div class="message-bubble voice-content">
          <div class="voice-wrapper">
            <div class="voice-controls">
              <el-button 
                :icon="getVoicePlayIcon('test-voice-2')" 
                circle 
                size="small"
                @click="toggleVoicePlay('test-voice-2')"
                :loading="voiceStates['test-voice-2']?.loading"
                class="voice-play-btn">
              </el-button>
              <span class="voice-duration">3"</span>
            </div>
            <div class="voice-visual-container">
              <AvWaveform
                v-if="testAudioUrl2"
                :audio-src="testAudioUrl2"
                :canv-width="280"
                :canv-height="40"
                :played-line-width="2"
                :noplayed-line-width="1"
                :played-line-color="'#1976d2'"
                :noplayed-line-color="'#ddd'"
                :playtime-clickable="true"
                :playtime-with-ms="false"
                ref-link="audioRef2"
                @click="handleWaveformClick"
                class="voice-waveform">
              </AvWaveform>
              <div v-else class="voice-loading">
                <span>加载中...</span>
              </div>
            </div>
            <audio
              ref="audioRef2"
              :src="testAudioUrl2"
              @play="handleAudioPlay('test-voice-2')"
              @pause="handleAudioPause('test-voice-2')"
              @ended="handleAudioEnded('test-voice-2')"
              @error="handleAudioError('test-voice-2')"
              @loadedmetadata="handleAudioLoaded('test-voice-2')"
              style="display: none;">
            </audio>
          </div>
        </div>
      </div>
      
      <div class="avatar">
        <img src="../assets/人员头像.png" alt="头像">
      </div>
    </div>

    <!-- 测试说明 -->
    <div class="test-info">
      <h3>测试说明：</h3>
      <ul>
        <li>这是一个语音消息播放功能的测试页面</li>
        <li>使用了WaveSurfer.js来实现音频波形显示和播放控制</li>
        <li>支持播放/暂停切换</li>
        <li>显示音频波形可视化</li>
        <li>响应式设计，适配不同屏幕尺寸</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { VideoPlay, VideoPause, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { AvWaveform } from 'vue-audio-visual'

// vue-audio-visual 相关状态管理
const voiceStates = ref({})
const currentPlayingVoice = ref(null)

// 测试音频URL
const testAudioUrl1 = ref('')
const testAudioUrl2 = ref('')

// 初始化语音播放状态
const initVoiceState = (msgid) => {
  if (!voiceStates.value[msgid]) {
    voiceStates.value[msgid] = {
      isPlaying: false,
      loading: false,
      duration: 0,
      currentTime: 0
    }
  }
}

// 获取语音播放按钮图标
const getVoicePlayIcon = (msgid) => {
  const state = voiceStates.value[msgid]
  if (state?.loading) return Loading
  return state?.isPlaying ? VideoPause : VideoPlay
}

// 处理波形点击事件
const handleWaveformClick = (event) => {
  console.log('波形被点击:', event)
}

// 处理音频播放事件
const handleAudioPlay = (msgid) => {
  voiceStates.value[msgid].isPlaying = true
  currentPlayingVoice.value = msgid
  console.log('音频开始播放:', msgid)
}

// 处理音频暂停事件
const handleAudioPause = (msgid) => {
  voiceStates.value[msgid].isPlaying = false
  if (currentPlayingVoice.value === msgid) {
    currentPlayingVoice.value = null
  }
  console.log('音频暂停:', msgid)
}

// 处理音频结束事件
const handleAudioEnded = (msgid) => {
  voiceStates.value[msgid].isPlaying = false
  currentPlayingVoice.value = null
  voiceStates.value[msgid].currentTime = 0
  console.log('音频播放结束:', msgid)
}

// 处理音频错误事件
const handleAudioError = (msgid) => {
  console.error('音频播放错误:', msgid)
  voiceStates.value[msgid].loading = false
  voiceStates.value[msgid].isPlaying = false
  ElMessage.error('语音播放失败')
}

// 处理音频加载完成事件
const handleAudioLoaded = (msgid) => {
  voiceStates.value[msgid].loading = false
  console.log('音频加载完成:', msgid)
}



// 切换语音播放状态
const toggleVoicePlay = async (msgid) => {
  try {
    initVoiceState(msgid)

    // 获取对应的audio元素
    const audioElement = msgid === 'test-voice-1' ?
      document.querySelector('audio[ref="audioRef1"]') :
      document.querySelector('audio[ref="audioRef2"]')

    if (!audioElement) {
      console.error('音频元素未找到:', msgid)
      return
    }

    // 暂停其他正在播放的语音
    if (currentPlayingVoice.value && currentPlayingVoice.value !== msgid) {
      const otherAudio = currentPlayingVoice.value === 'test-voice-1' ?
        document.querySelector('audio[ref="audioRef1"]') :
        document.querySelector('audio[ref="audioRef2"]')
      if (otherAudio && !otherAudio.paused) {
        otherAudio.pause()
      }
    }

    // 切换播放状态
    if (voiceStates.value[msgid].isPlaying) {
      audioElement.pause()
    } else {
      audioElement.play()
    }
  } catch (error) {
    console.error('语音播放切换失败:', error)
    voiceStates.value[msgid].loading = false
    ElMessage.error('语音播放失败')
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化测试语音状态
  initVoiceState('test-voice-1')
  initVoiceState('test-voice-2')

  // 设置测试音频URL（使用在线测试音频）
  testAudioUrl1.value = 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  testAudioUrl2.value = 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
})

// 组件卸载时清理资源
onUnmounted(() => {
  voiceStates.value = {}
  currentPlayingVoice.value = null
})
</script>

<style scoped>
.voice-test-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.voice-test-container h2 {
  text-align: center;
  color: #303133;
  margin-bottom: 30px;
}

/* 消息容器样式 */
.message-container {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
  gap: 12px;
}

.message-container.sent {
  flex-direction: row-reverse;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sender-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.sender-name {
  font-weight: 500;
  color: #303133;
}

.sender-lable {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

.sender-lable.wechat {
  background-color: #e8f5e8;
  color: #67c23a;
}

.sender-lable.employee {
  background-color: #e3f2fd;
  color: #409eff;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 12px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e4e7ed;
}

.sent .message-bubble {
  background-color: #e3f2fd;
  border-color: #bbdefb;
}

/* 语音消息样式 */
.voice-content {
  min-width: 200px;
  max-width: 350px;
  transition: all 0.3s ease;
}

.voice-content:hover {
  background-color: #f5f7fa;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sent .voice-content:hover {
  background-color: #d6eafb;
}

.voice-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.voice-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.voice-play-btn {
  width: 36px;
  height: 36px;
  border: none;
  background-color: #5188ff;
  color: white;
  transition: all 0.3s ease;
}

.voice-play-btn:hover {
  background-color: #4070ff;
  transform: scale(1.05);
}

.voice-play-btn:active {
  transform: scale(0.95);
}

.sent .voice-play-btn {
  background-color: #1976d2;
}

.sent .voice-play-btn:hover {
  background-color: #1565c0;
}

.voice-duration {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  margin-left: auto;
}

.voice-visual-container {
  width: 100%;
  height: 40px;
  position: relative;
  background-color: #f8f9fa;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sent .voice-visual-container {
  background-color: #e8f4fd;
}

.voice-waveform {
  width: 100%;
  height: 100%;
}

.voice-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  width: 100%;
  height: 100%;
  color: #999;
  font-size: 12px;
}

/* 音频播放时的动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.voice-content.playing .voice-play-btn {
  animation: pulse 1.5s infinite;
}

/* 测试说明样式 */
.test-info {
  margin-top: 40px;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.test-info h3 {
  color: #303133;
  margin-bottom: 15px;
}

.test-info ul {
  color: #606266;
  line-height: 1.6;
}

.test-info li {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .voice-test-container {
    padding: 15px;
  }

  .message-content {
    max-width: 85%;
  }

  .voice-content {
    min-width: 180px;
    max-width: 280px;
    padding: 10px 12px;
  }

  .voice-play-btn {
    width: 32px;
    height: 32px;
  }

  .voice-visual-container {
    height: 35px;
  }
}
</style>
