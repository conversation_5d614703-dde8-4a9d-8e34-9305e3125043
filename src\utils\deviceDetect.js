export const isMobileDevice = () => {
    // 检查 userAgent
    const userAgent = navigator.userAgent.toLowerCase();
    const mobileKeywords = ['android', 'iphone', 'ipod', 'ipad', 'windows phone', 'mobile'];
    const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword));

    // 检查屏幕宽度（小于768px认为是移动设备）
    const isMobileWidth = window.innerWidth < 768;

    // 检查是否支持触摸事件
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // 综合判断：任意两个条件满足即认为是移动设备
    let mobileConditions = [isMobileUA, isMobileWidth, isTouchDevice];
    return mobileConditions.filter(condition => condition).length >= 2;
}; 