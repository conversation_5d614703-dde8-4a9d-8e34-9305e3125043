{"version": 3, "file": "js/978.039fe18f.js", "mappings": "6dAqDA,MAAMA,GAAaC,EAAAA,EAAAA,IAAI,IACjBC,GAAaD,EAAAA,EAAAA,IAAI,SAEjBE,GAAeF,EAAAA,EAAAA,IAAI,IACnBG,GAAkBH,EAAAA,EAAAA,IAAI,IAEtBI,GAAcJ,EAAAA,EAAAA,IAAI,GAClBK,GAAWL,EAAAA,EAAAA,IAAI,KAEfM,GAAwBC,EAAAA,EAAAA,IAAO,yBAC/BC,GAA4BD,EAAAA,EAAAA,IAAO,6BAGnCE,EAAkBA,CAACC,EAASC,EAAYC,KAE1CN,EAAsBO,MAAQ,eAC9BL,EAA0BK,MAAQ,mBAGlC,MAAMC,EAAYC,aAAaC,QAAQ,gBAEvCC,EAAAA,EAAcC,KAAK,2BAA4B,CAC3CC,KAAM,WACNC,KAAMV,EACNW,MAAOV,EACPW,KAAMV,GACP,CACCW,QAAS,CACLC,cAAe,UAAYV,KAE/BW,MAAKC,IAEiB,IAAlBA,EAAIC,KAAKC,OAET1B,EAAaW,MAAQa,EAAIC,KAAKA,KAAKE,OAEnC1B,EAAgBU,MAAQa,EAAIC,KAAKA,KAAKG,MAE1C,IAGDC,OAAMC,IACLC,QAAQC,IAAIF,GACZG,EAAAA,GAAUH,MAAM,uBAAuB,GACzC,EAIAI,GAAmBC,EAAAA,EAAAA,KAAS,IAGvBnC,EAAaW,SAIxByB,EAAAA,EAAAA,IAAMF,GAAmBG,IACrBN,QAAQC,IAAI,SAAUK,EAAOC,OAAO,IAQxC,MAAMC,EAAoBrB,IACtBhB,EAAYS,MAAQO,EACpBX,EAAgBL,EAAYS,MAAOR,EAASQ,MAAOd,EAAWc,MAAM,EAGlE6B,EAAkBA,KAChB3C,EAAWc,QACXT,EAAYS,MAAQ,GAExBJ,EAAgBL,EAAYS,MAAOR,EAASQ,MAAOd,EAAWc,MAAM,GAGxE8B,EAAAA,EAAAA,KAAU,IAAMlC,EAAgB,EAAG,IAAK,OACxC6B,EAAAA,EAAAA,IAAMvC,EAAY2C,GAKlB,MAAME,GAAmBrC,EAAAA,EAAAA,IAAO,oBAAoBP,EAAAA,EAAAA,IAAI,OAGlD6C,EAAuBC,IAErBF,IACAA,EAAiB/B,MAAQ,CACrBS,KAAMwB,EAASxB,KACfyB,OAAQD,EAASC,OACjBC,MAAOC,EAAoBH,GAC3BI,GAAIJ,EAASK,gBAAkB,KAGvClB,QAAQC,IAAI,QAASY,EAASxB,KAAK,EAGjC8B,EAAcA,CAACC,EAAKC,KACtBrB,QAAQC,IAAImB,EAAKC,EAAM,EAKrBC,EAAsBpC,IACxB,OAAQA,GACJ,KAAK,EACD,MAAO,SACX,KAAK,EACD,MAAO,UACX,QACI,MAAO,UACf,EAIE8B,EAAuBH,IAEzB,OAAQA,EAAS3B,MACb,KAAK,EAID,MAH+B,oCAA3B2B,EAASK,gBACTlB,QAAQC,IAAI,QAET,MACX,KAAK,EACD,OAAOY,EAASU,SAAW,IAAIV,EAASU,WAAa,QACzD,QACI,MAAO,GACf,E,o3CChLJ,MAAMC,EAAc,EAEpB,O", "sources": ["webpack://wccsfront/./src/components/ChatArchive/CustomerChat/CustomerList.vue", "webpack://wccsfront/./src/components/ChatArchive/CustomerChat/CustomerList.vue?f0a6"], "sourcesContent": ["<!-- TODO：\r\n固定搜索框的宽度 -->\r\n<template>\r\n    <div class=\"mainContainer-customerlist\">\r\n        <div class=\"search\" style=\"width: 800px;\">\r\n            <el-input placeholder=\"搜索客户\" v-model=\"searchWord\" clearable class=\"search-input\">\r\n                <template #prefix>\r\n                    <el-icon class=\"search-icon\">\r\n                        <Search />\r\n                    </el-icon>\r\n                </template>\r\n            </el-input>\r\n        </div>\r\n\r\n        <div class=\"lable-navigator\">\r\n            <el-tabs v-model=\"activeName\" class=\"customerList-tabs\" @tab-click=\"handleClick\">\r\n                <el-tab-pane label=\"按客户\" name=\"first\">\r\n                    <div class=\"customer-card\" v-for=\"customer in archivedCustomer\" :key=\"customer.name\"\r\n                        @click=\"handlecustomerClick(customer)\">\r\n                        <div class=\"profile\">\r\n                            <img class=\"staff-avatar\" :src=\"customer.avatar\" alt=\"人员头像\">\r\n                        </div>\r\n                        <div class=\"info\">\r\n                            <p class=\"name\">{{ customer.name }}</p>\r\n                            <p :class=\"['lable', handleCustomerType(customer.type)]\">{{ getCustomerCorpName(customer) }}\r\n                            </p>\r\n                        </div>\r\n                        <!-- <div class=\"mark\">\r\n                            <el-tag size=\"small\" type=\"primary\" effect=\"plain\"></el-tag>\r\n                        </div> -->\r\n                    </div>\r\n                </el-tab-pane>\r\n                <!-- <el-tab-pane label=\"Config\" name=\"second\">Config</el-tab-pane>\r\n                <el-tab-pane label=\"Role\" name=\"third\">Role</el-tab-pane>\r\n                <el-tab-pane label=\"Task\" name=\"fourth\">Task</el-tab-pane> -->\r\n            </el-tabs>\r\n        </div>\r\n\r\n        <div class=\"pageContainer\">\r\n            <el-pagination v-model:current-page=\"currentPage\" background :size=\"size\" layout=\"total, prev, pager, next\"\r\n                :total=\"totalCoustomers\" :page-size=\"pageSize\" :pager-count=\"3\" small\r\n                @current-change=\"handlePageChange\">\r\n            </el-pagination>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { Search } from '@element-plus/icons-vue';\r\nimport { ref, onMounted, watch, inject, computed } from 'vue';\r\nimport axiosInstance from '@/axiosConfig.js'; // 引入 axios 实例\r\nimport { ElMessage } from 'element-plus';\r\n\r\nconst searchWord = ref(''); // searchWord 控制搜索框\r\nconst activeName = ref('first') // activeName 控制 tab 切换\r\n\r\nconst customerList = ref([]); // customerList 人员列表\r\nconst totalCoustomers = ref('');//customerList 人员总数\r\n\r\nconst currentPage = ref(1); // currentPage 当前页码\r\nconst pageSize = ref(100); // pageSize 每页显示数量\r\n\r\nconst currentComponent_List = inject('currentComponent_List')\r\nconst currentComponent_ChatList = inject('currentComponent_ChatList')\r\n\r\n// -------------------------------------------------------------------------- 获取存档人员列表与搜索实现\r\nconst getCustomerList = (curPage, maxPageNum, searchName) => {\r\n\r\n    currentComponent_List.value = 'CustomerList';\r\n    currentComponent_ChatList.value = 'CustomerChatList';\r\n\r\n    // 获取存档人员列表\r\n    const jwt_token = localStorage.getItem('access_token')\r\n\r\n    axiosInstance.post('/api/chatmessage/chatter', {\r\n        type: \"customer\", // List_search_type 列表搜索类型\r\n        page: curPage,\r\n        limit: maxPageNum,\r\n        name: searchName\r\n    }, {\r\n        headers: {\r\n            Authorization: 'Bearer ' + jwt_token\r\n        },\r\n    },).then(res => {\r\n        // console.log(res.data.staffList.length);\r\n        if (res.data.code === 0) {\r\n            // console.log('获取人员列表成功', res.data.data.result)\r\n            customerList.value = res.data.data.result;\r\n            //此处的数量是所获取的人员列表的数量，非存档员工列表数量\r\n            totalCoustomers.value = res.data.data.total;\r\n            // console.log('totalCoustomers为',totalCoustomers.value)\r\n        }\r\n\r\n\r\n    }).catch(error => {\r\n        console.log(error);\r\n        ElMessage.error('获取客户列表失败，请检查网络或联系管理员');\r\n    });\r\n}\r\n\r\n// 计算属性：过滤\r\nconst archivedCustomer = computed(() => {\r\n    // console.log('已过滤存档员工')\r\n    // return customerList.value.filter(customer => customer.sessionArchiveFlag === 1);\r\n    return customerList.value\r\n});\r\n\r\n// 更新总数计算\r\nwatch(archivedCustomer, (newVal) => {\r\n    console.log('当页客户总数', newVal.length)\r\n    // totalCoustomers.value = newVal.length;\r\n});\r\n\r\n\r\n\r\n\r\n//页码变化时触发\r\nconst handlePageChange = (page) => {\r\n    currentPage.value = page;\r\n    getCustomerList(currentPage.value, pageSize.value, searchWord.value);\r\n}\r\n//搜索框变化时触发\r\nconst filterCustomers = () => {\r\n    if (searchWord.value) {\r\n        currentPage.value = 1; // 重置为第一页\r\n    }\r\n    getCustomerList(currentPage.value, pageSize.value, searchWord.value);\r\n}\r\n\r\nonMounted(() => getCustomerList(1, 100, ''));\r\nwatch(searchWord, filterCustomers);\r\n\r\n// -------------------------------------------------------------------------- 点击员工卡片，传递员工信息（名称，头像）至 CustomerChatList 组件\r\n\r\n// 提供给其他组件使用的员工信息\r\nconst selectedCustomer = inject('selectedCustomer', ref(null));\r\n\r\n// 处理员工卡片点击事件\r\nconst handlecustomerClick = (customer) => {\r\n    // 将选中的员工信息传递给CustomerChatList组件\r\n    if (selectedCustomer) {\r\n        selectedCustomer.value = {\r\n            name: customer.name,\r\n            avatar: customer.avatar,\r\n            label: getCustomerCorpName(customer),\r\n            id: customer.externalUserId || '',\r\n        };\r\n    }\r\n    console.log('选中员工:', customer.name);\r\n};\r\n\r\nconst handleClick = (tab, event) => {\r\n    console.log(tab, event)\r\n}\r\n\r\n\r\n// ----------------------------------------------------控制客户类型显示：1-微信 2-企业微信\r\nconst handleCustomerType = (type) => {\r\n    switch (type) {\r\n        case 1:\r\n            return \"wechat\";\r\n        case 2:\r\n            return \"ewechat\";\r\n        default:\r\n            return \"unknown\";\r\n    }\r\n};\r\n\r\n// ----------------------------------------------------控制客户标签显示：1-@微信 <EMAIL>\r\nconst getCustomerCorpName = (customer) => {\r\n\r\n    switch (customer.type) {\r\n        case 1:\r\n            if (customer.externalUserId == 'wmykZGPQAApQrCvSTyJRFBdyOQrDpjUg') {\r\n                console.log('触发点7')\r\n            }\r\n            return \"@微信\";\r\n        case 2:\r\n            return customer.corpName ? `@${customer.corpName}` : '@未知企业';\r\n        default:\r\n            return \"\";\r\n    }\r\n}\r\n\r\n\r\n\r\n</script>\r\n\r\n<style>\r\n.mainContainer-customerlist {\r\n    margin: 0;\r\n    height: calc(100vh - 6.969rem);\r\n    background-color: #ffffff;\r\n    display: flex;\r\n    flex-direction: column;\r\n    width: 15.625rem;\r\n    padding: 1rem 0.75rem;\r\n    border-right: 0.0625rem solid #e6e6e6;\r\n    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.02);\r\n}\r\n\r\n.search {\r\n    margin-bottom: 0.3125rem;\r\n}\r\n\r\n.search-input {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.search-input:hover .el-input__wrapper {\r\n    box-shadow: 0 0 0 0.0625rem #e6e6e6 inset;\r\n}\r\n\r\n.search-icon {\r\n    color: #909399;\r\n}\r\n\r\n.lable-navigator {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar {\r\n    width: 0.375rem;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar-thumb {\r\n    background-color: #e0e0e0;\r\n    border-radius: 0.1875rem;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar-track {\r\n    background-color: transparent;\r\n}\r\n\r\n.customer-card {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    height: 4rem;\r\n    padding: 0 0.75rem;\r\n    margin: 0.25rem 0;\r\n    border-radius: 0.5rem;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.customer-card:hover {\r\n    background-color: #f5f7fa;\r\n    transform: translateX(0.125rem);\r\n}\r\n\r\n.customer-card .profile {\r\n    /* background: linear-gradient(135deg, #1890ff, #0960bd); */\r\n    /* color: #ffffff; */\r\n    width: 2.5rem;\r\n    height: 2.5rem;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    border-radius: 0.5rem;\r\n    margin-right: 0.75rem;\r\n    /* font-weight: 500; */\r\n    box-shadow: 0 0.125rem 0.25rem rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n.customer-card .profile img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.customer-card .info {\r\n    flex: 1;\r\n}\r\n\r\n.customer-card .info .name {\r\n    font-size: 0.875rem;\r\n    font-weight: 500;\r\n    color: #2c3e50;\r\n    margin-bottom: 0.25rem;\r\n}\r\n\r\n.customer-card .info .lable.unknown {\r\n    font-size: 0.75rem;\r\n    color: #909399;\r\n}\r\n\r\n.customer-card .info .lable.wechat {\r\n    font-size: 0.75rem;\r\n    color: #4bdd1f;\r\n}\r\n\r\n.customer-card .info .lable.ewechat {\r\n    font-size: 0.75rem;\r\n    color: #fd6c33;\r\n}\r\n\r\n.customer-card .mark {\r\n    margin-left: 0.5rem;\r\n}\r\n\r\n.pageContainer {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 0.75rem 0;\r\n    margin-top: 0.5rem;\r\n    border-top: 0.0625rem solid #f0f0f0;\r\n    width: 100%;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination) {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    padding: 0 0.5rem;\r\n    font-size: 0.8125rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pagination__total) {\r\n    min-width: auto;\r\n    margin-right: 0.5rem;\r\n    font-size: 0.8125rem;\r\n    color: #606266;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager) {\r\n    margin: 0;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager li) {\r\n    min-width: 1.5rem;\r\n    height: 1.5rem;\r\n    line-height: 1.5rem;\r\n    font-weight: normal;\r\n    margin: 0 0.125rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager li.active) {\r\n    background-color: #1890ff;\r\n    color: #ffffff;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination button) {\r\n    min-width: 1.5rem;\r\n    height: 1.5rem;\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .btn-prev),\r\n.pageContainer :deep(.el-pagination .btn-next) {\r\n    margin: 0 0.125rem;\r\n    background-color: #f4f4f5;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-icon) {\r\n    font-size: 0.75rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .more) {\r\n    background-color: transparent;\r\n}\r\n\r\n.customerList-tabs {\r\n    height: 100%;\r\n}\r\n\r\n.customerList-tabs :deep(.el-tabs__header) {\r\n    margin-bottom: 1rem;\r\n}\r\n\r\n.customerList-tabs :deep(.el-tabs__nav-wrap::after) {\r\n    height: 0.0625rem;\r\n    background-color: #f0f0f0;\r\n}\r\n\r\n.customerList-tabs :deep(.el-tabs__item) {\r\n    font-size: 0.875rem;\r\n    color: #606266;\r\n    padding: 0 1rem;\r\n}\r\n\r\n.customerList-tabs :deep(.el-tabs__item.is-active) {\r\n    color: #1890ff;\r\n    font-weight: 500;\r\n}\r\n\r\n.customerList-tabs :deep(.el-tabs__active-bar) {\r\n    background-color: #1890ff;\r\n    height: 0.125rem;\r\n}\r\n</style>\r\n", "import script from \"./CustomerList.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./CustomerList.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./CustomerList.vue?vue&type=style&index=0&id=e769bb20&lang=css\"\n\nconst __exports__ = script;\n\nexport default __exports__"], "names": ["searchWord", "ref", "activeName", "customerList", "totalCoustomers", "currentPage", "pageSize", "currentComponent_List", "inject", "currentComponent_ChatList", "getCustomerList", "curPage", "maxPageNum", "searchName", "value", "jwt_token", "localStorage", "getItem", "axiosInstance", "post", "type", "page", "limit", "name", "headers", "Authorization", "then", "res", "data", "code", "result", "total", "catch", "error", "console", "log", "ElMessage", "archivedCustomer", "computed", "watch", "newVal", "length", "handlePageChange", "filterCustomers", "onMounted", "selectedCustomer", "handlecustomerClick", "customer", "avatar", "label", "getCustomerCorpName", "id", "externalUserId", "handleClick", "tab", "event", "handleCustomerType", "corpName", "__exports__"], "sourceRoot": ""}