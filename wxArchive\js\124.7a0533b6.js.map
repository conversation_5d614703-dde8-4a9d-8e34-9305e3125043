{"version": 3, "file": "js/124.7a0533b6.js", "mappings": "42BAoFA,MAAMA,GAAaC,EAAAA,EAAAA,IAAI,IACjBC,GAAaD,EAAAA,EAAAA,IAAI,SAEjBE,GAAYF,EAAAA,EAAAA,IAAI,IAChBG,GAAkBH,EAAAA,EAAAA,IAAI,IACtBI,GAAmBJ,EAAAA,EAAAA,IAAI,IACvBK,GAAcL,EAAAA,EAAAA,IAAI,IAElBM,GAAcN,EAAAA,EAAAA,IAAI,GAClBO,GAAWP,EAAAA,EAAAA,IAAI,KAEfQ,GAAwBC,EAAAA,EAAAA,IAAO,yBAC/BC,GAA4BD,EAAAA,EAAAA,IAAO,6BAGnCE,EAAeA,CAACC,EAASC,EAAYC,KACvCN,EAAsBO,MAAQ,YAC9BL,EAA0BK,MAAQ,GAElC,MAAMC,EAAYC,aAAaC,QAAQ,gBAEvCC,EAAAA,EAAcC,KAAK,2BAA4B,CAC3CC,KAAM,YACNC,KAAMV,EACNW,MAAOV,EACPW,KAAMV,GACP,CACCW,QAAS,CACLC,cAAe,UAAYV,KAE/BW,MAAKC,IACiB,IAAlBA,EAAIC,KAAKC,OACTC,QAAQC,IAAI,WAAYJ,EAAIC,KAAKA,KAAKI,QACtC/B,EAAUa,MAAQa,EAAIC,KAAKA,KAAKI,OAChC5B,EAAYU,MAAQa,EAAIC,KAAKA,KAAKK,MAGlC/B,EAAgBY,MAAQb,EAAUa,MAAMoB,QAAOC,IAAUA,EAAMC,WAC/DjC,EAAiBW,MAAQb,EAAUa,MAAMoB,QAAOC,GAA4B,aAAnBA,EAAMC,WAG/DnC,EAAUa,MAAMuB,SAAQF,IAChBA,EAAMG,OACNC,EAAaJ,EAAMG,OAEnBH,EAAMK,QACNC,GAAqBN,EAAMK,OAC/B,IAER,IACDE,OAAMC,IACLb,QAAQC,IAAIY,GACZC,EAAAA,GAAUD,MAAM,uBAAuB,GACzC,EAIAE,EAAmBA,KACrBf,QAAQC,IAAI,GAAG,EAGbe,EAAkBA,KAChBhD,EAAWgB,QACXT,EAAYS,MAAQ,GAGxB,MAAMiC,EAAY,IAAI7C,EAAgBY,SAAUX,EAAiBW,OAC3DkC,EAAiBD,EAAUb,QAAOC,GACpCA,EAAMZ,KAAK0B,cAAcC,SAASpD,EAAWgB,MAAMmC,iBAIvD,OAAQjD,EAAWc,OACf,IAAK,QACDb,EAAUa,MAAQkC,EAClB5C,EAAYU,MAAQb,EAAUa,MAAMqC,OACpC,MACJ,IAAK,SACDlD,EAAUa,MAAQkC,EAAed,QAAOC,IAAUA,EAAMC,WACxDhC,EAAYU,MAAQb,EAAUa,MAAMqC,OACpC,MACJ,IAAK,QACDlD,EAAUa,MAAQkC,EAAed,QAAOC,GAA4B,aAAnBA,EAAMC,WACvDhC,EAAYU,MAAQb,EAAUa,MAAMqC,OACpC,MACR,GAIJC,EAAAA,EAAAA,KAAU,IAAM1C,EAAa,EAAG,IAAK,OACrC2C,EAAAA,EAAAA,IAAMvD,EAAYgD,GAElB,MAAMQ,EAAeC,IAGjB,OAFAzB,QAAQC,IAAI,aAAcwB,EAAIC,MAAMjC,MAE5BgC,EAAIC,MAAMjC,MACd,IAAK,QACDtB,EAAUa,MAAQ,IAAIZ,EAAgBY,SAAUX,EAAiBW,OACjEV,EAAYU,MAAQb,EAAUa,MAAMqC,OACpC,MACJ,IAAK,SACDlD,EAAUa,MAAQZ,EAAgBY,MAClCV,EAAYU,MAAQb,EAAUa,MAAMqC,OACpC,MACJ,IAAK,QACDlD,EAAUa,MAAQX,EAAiBW,MACnCV,EAAYU,MAAQb,EAAUa,MAAMqC,OACpC,MACR,EAIEM,GAAejD,EAAAA,EAAAA,IAAO,eAAgB,MAGtCkD,EAAoBvB,IACtB,IAAIwB,EAAOxB,EAAMK,OACboB,EAAK,GAELC,EAAWC,EAAa3B,GACxB4B,EAAY,GACZC,EAAa,GAEbC,EAAS,GACTC,EAAW,GACXC,EAAU,GAEV/C,EAAO,YACPC,EAAO,EACPC,EAAQ,IAEZ,MAAM8C,EAAoB,CACtBT,KAAMA,EACNC,GAAIA,EAEJC,SAAUA,EACVE,UAAWA,EACXC,WAAYA,EAEZC,OAAQA,EACRC,SAAUA,EACVC,QAASA,EAET/C,KAAMA,EACNC,KAAMA,EACNC,MAAOA,GAEXmC,EAAa3C,MAAQsD,EAEjBtC,QAAQC,IAAI,aAAc0B,EAAa3C,MAAM,EAQ/CgD,EAAgB3B,IAClB,GAAIA,EAAMZ,KACN,OAAOY,EAAMZ,KAEjB,MAAM8C,EAAUlC,EAAMK,OAChB8B,EAAY,KAClB,OAAID,GAAWA,EAAQlB,OAAS,GAC5BhB,EAAMZ,KAAO+C,EAAYD,EAAQE,MAAM,EAAG,GAAI,MACvCD,EAAYD,EAAQE,MAAM,EAAG,GAAK,OAEtC,EAAE,EAKPC,GAAqBzE,EAAAA,EAAAA,IAAI,CAAC,GAC1B0E,GAA2B1E,EAAAA,EAAAA,IAAI,CAAC,GAEhC2E,GAAmB3E,EAAAA,EAAAA,IAAI,IAAI4E,KAG3BC,GAAa7E,EAAAA,EAAAA,IAAI,CAAC,GAElBwC,EAAesC,UAEjB,GAAID,EAAW9D,MAAMgE,GACjB,OAAOF,EAAW9D,MAAMgE,GAI5B,GAAIJ,EAAiB5D,MAAMiE,IAAID,IAAgBL,EAAyB3D,MAAMgE,GAAc,CACxF,MAAME,EAAaP,EAAyB3D,MAAMgE,GAElD,OADAF,EAAW9D,MAAMgE,GAAeE,GAAYC,UAAU1D,MAAQ,OACvDqD,EAAW9D,MAAMgE,EAC5B,CAEA,IAEIJ,EAAiB5D,MAAMoE,IAAIJ,GAE3BhD,QAAQC,IAAI,oBACZ,MAAMhB,EAAYC,aAAaC,QAAQ,gBACjCkE,QAAiBjE,EAAAA,EAAckE,IAAI,0CAA2C,CAChFC,OAAQ,CAAEP,eACVtD,QAAS,CAAEC,cAAe,UAAYV,KAEpCa,EAAOuD,EAASvD,KAAKA,KAI3B,OAHA4C,EAAmB1D,MAAMgE,GAAelD,EACxC6C,EAAyB3D,MAAMgE,GAAelD,EAC9CgD,EAAW9D,MAAMgE,GAAelD,EAAKqD,UAAU1D,KACxCqD,EAAW9D,MAAMgE,EAC5B,CAAE,MAAOnC,GAKL,OAJAb,QAAQa,MAAM,iBAAkBA,GAChC6B,EAAmB1D,MAAMgE,GAAe,CAAEG,UAAW,CAAE1D,KAAM,SAC7DkD,EAAyB3D,MAAMgE,GAAe,CAAEG,UAAW,CAAE1D,KAAM,SACnEqD,EAAW9D,MAAMgE,GAAe,OACzB,MACX,CAAE,QAEEJ,EAAiB5D,MAAMwE,OAAOR,EAClC,GAIES,GAAcxF,EAAAA,EAAAA,IAAI,CAAC,GACnByF,GAAoBzF,EAAAA,EAAAA,IAAI,CAAC,GAEzB0F,IAAoB1F,EAAAA,EAAAA,IAAI,IAAI4E,KAG5BlC,GAAuBoC,UAGzB,IAAIY,GAAkB3E,MAAMiE,IAAIvC,GAAhC,CAGA,GAAIgD,EAAkB1E,MAAM0B,GAAS,CACjC,MAAMwC,EAAaQ,EAAkB1E,MAAM0B,GAE3C,OADA+C,EAAYzE,MAAM0B,GAAUwC,GAAc,EACnCA,CACX,CAEA,IAEIN,EAAiB5D,MAAMoE,IAAI1C,GAE3BV,QAAQC,IAAI,4BAEZ,MAAMhB,EAAYC,aAAaC,QAAQ,gBAEjCkE,QAAiBjE,EAAAA,EAAckE,IAAI,+CAAgD,CACrFC,OAAQ,CAAE7C,UACVhB,QAAS,CAAEC,cAAe,UAAYV,KAEpCa,EAAOuD,EAASvD,KAAKA,KAI3B,OAHA2D,EAAYzE,MAAM0B,GAAUZ,EAC5B4D,EAAkB1E,MAAM0B,GAAUZ,EAClC2D,EAAYzE,MAAM0B,GAAUZ,EACrB2D,EAAYzE,MAAM0B,EAC7B,CAAE,MAAOG,GAIL,OAHAb,QAAQa,MAAM,YAAaA,GAC3B4C,EAAYzE,MAAM0B,GAAU,EAC5BgD,EAAkB1E,MAAM0B,GAAU,EAC3B,CACX,CAAE,QAEEiD,GAAkB3E,MAAMwE,OAAO9C,EACnC,CAhCA,CAgCA,E,69ECvVJ,MAAMkD,EAAc,EAEpB,O", "sources": ["webpack://wccsfront/./src/components/ChatArchive/GroupChat/GroupList.vue", "webpack://wccsfront/./src/components/ChatArchive/GroupChat/GroupList.vue?6bfe"], "sourcesContent": ["<!-- TODO：\r\n固定搜索框的宽度 -->\r\n<template>\r\n    <div class=\"mainContainer-grouplist\">\r\n        <div class=\"search\" style=\"width: 800px;\">\r\n            <el-input placeholder=\"搜索群聊\" v-model=\"searchWord\" clearable class=\"search-input\">\r\n                <template #prefix>\r\n                    <el-icon class=\"search-icon\">\r\n                        <Search />\r\n                    </el-icon>\r\n                </template>\r\n            </el-input>\r\n        </div>\r\n\r\n        <div class=\"lable-navigator\">\r\n            <el-tabs v-model=\"activeName\" class=\"grouplist-tabs\" @tab-click=\"handleClick\">\r\n                <el-tab-pane label=\"全部\" name=\"first\">\r\n                    <div class=\"group-card\" v-for=\"group in grouplist\" :key=\"group.roomid\"\r\n                        @click=\"handleGroupClick(group)\">\r\n                        <div class=\"profile\">\r\n                            <img class=\"staff-avatar\" src=\"@/assets/群聊头像.png\" alt=\"群聊头像\">\r\n                        </div>\r\n                        <div class=\"info\">\r\n                            <p class=\"name\">{{ getGroupName(group) }}</p>\r\n                            <div class=\"info-detail\">\r\n                                <p class=\"totalNum\">{{ goup_chatId_cache[group.chatId] || '加载中...' }}人 | </p>\r\n                                <p class=\"owner\">群主：{{ ownerNames[group.owner] || '加载中...' }}</p>\r\n                            </div>\r\n                        </div>\r\n\r\n                    </div>\r\n                </el-tab-pane>\r\n                <el-tab-pane label=\"内部\" name=\"second\">\r\n                    <div class=\"group-card\" v-for=\"group in grouplist\" :key=\"group.roomid\"\r\n                        @click=\"handleGroupClick(group)\">\r\n                        <div class=\"profile\">\r\n                            <img class=\"staff-avatar\" src=\"@/assets/群聊头像.png\" alt=\"群聊头像\">\r\n                        </div>\r\n                        <div class=\"info\">\r\n                            <p class=\"name\">{{ getGroupName(group) }}</p>\r\n                            <div class=\"info-detail\">\r\n                                <p class=\"totalNum\">{{ goup_chatId_cache[group.chatId] || '加载中...' }}人 | </p>\r\n                                <p class=\"owner\">群主：{{ ownerNames[group.owner] || '加载中...' }}</p>\r\n                            </div>\r\n                        </div>\r\n\r\n                    </div>\r\n                </el-tab-pane>\r\n                <el-tab-pane label=\"外部\" name=\"third\">\r\n                    <div class=\"group-card\" v-for=\"group in grouplist\" :key=\"group.roomid\"\r\n                        @click=\"handleGroupClick(group)\">\r\n                        <div class=\"profile\">\r\n                            <img class=\"staff-avatar\" src=\"@/assets/群聊头像.png\" alt=\"群聊头像\">\r\n                        </div>\r\n                        <div class=\"info\">\r\n                            <p class=\"name\">{{ getGroupName(group) }}</p>\r\n                            <div class=\"info-detail\">\r\n                                <p class=\"totalNum\">{{ goup_chatId_cache[group.chatId] || '加载中...' }}人 | </p>\r\n                                <p class=\"owner\">群主：{{ ownerNames[group.owner] || '加载中...' }}</p>\r\n                            </div>\r\n                        </div>\r\n\r\n                    </div>\r\n                </el-tab-pane>\r\n                <!-- <el-tab-pane label=\"Config\" name=\"second\">Config</el-tab-pane>\r\n                <el-tab-pane label=\"Role\" name=\"third\">Role</el-tab-pane>\r\n                <el-tab-pane label=\"Task\" name=\"fourth\">Task</el-tab-pane> -->\r\n            </el-tabs>\r\n        </div>\r\n\r\n        <div class=\"pageContainer\">\r\n            <el-pagination v-model:current-page=\"currentPage\" background :size=\"size\" layout=\"total, prev, pager, next\"\r\n                :total=\"totalGroups\" :page-size=\"pageSize\" :pager-count=\"3\" small @current-change=\"handlePageChange\">\r\n            </el-pagination>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { Search } from '@element-plus/icons-vue';\r\nimport { ref, onMounted, watch, inject } from 'vue';\r\nimport axiosInstance from '@/axiosConfig.js'; // 引入 axios 实例\r\nimport { ElMessage } from 'element-plus';\r\n\r\nconst searchWord = ref(''); // searchWord 控制搜索框\r\nconst activeName = ref('first') // activeName 控制 tab 切换\r\n\r\nconst grouplist = ref([]); // grouplist 群聊列表-全部\r\nconst grouplist_inner = ref([]); // grouplist 群聊列表-内部\r\nconst grouplist_outter = ref([]); // grouplist 群聊列表-外部\r\nconst totalGroups = ref('');//grouplist 人员总数\r\n\r\nconst currentPage = ref(1); // currentPage 当前页码\r\nconst pageSize = ref(100); // pageSize 每页显示数量\r\n\r\nconst currentComponent_List = inject('currentComponent_List')\r\nconst currentComponent_ChatList = inject('currentComponent_ChatList')\r\n\r\n// -------------------------------------------------------------------------- 获取存档人员列表与搜索实现\r\nconst getgrouplist = (curPage, maxPageNum, searchName) => {\r\n    currentComponent_List.value = 'GroupList'\r\n    currentComponent_ChatList.value = ''\r\n\r\n    const jwt_token = localStorage.getItem('access_token')\r\n\r\n    axiosInstance.post('/api/chatmessage/chatter', {\r\n        type: \"groupchat\",\r\n        page: curPage,\r\n        limit: maxPageNum,\r\n        name: searchName\r\n    }, {\r\n        headers: {\r\n            Authorization: 'Bearer ' + jwt_token\r\n        },\r\n    },).then(res => {\r\n        if (res.data.code === 0) {\r\n            console.log('获取群聊列表成功', res.data.data.result)\r\n            grouplist.value = res.data.data.result;\r\n            totalGroups.value = res.data.data.total;\r\n\r\n            // 根据chatType分类群聊\r\n            grouplist_inner.value = grouplist.value.filter(group => !group.chatType);\r\n            grouplist_outter.value = grouplist.value.filter(group => group.chatType === 'customer');\r\n\r\n            // 获取每个群聊的群主名称\r\n            grouplist.value.forEach(group => {\r\n                if (group.owner) {\r\n                    getOwnerName(group.owner);\r\n                }\r\n                if (group.chatId) {\r\n                    getGroupMenberNumber(group.chatId)\r\n                }\r\n            });\r\n        }\r\n    }).catch(error => {\r\n        console.log(error);\r\n        ElMessage.error('获取群聊列表失败，请检查网络或联系管理员');\r\n    });\r\n}\r\n\r\n//页码变化时触发---限定一次性获取全部群聊，一般只有一页\r\nconst handlePageChange = () => {\r\n    console.log('')\r\n}\r\n//搜索框变化时触发\r\nconst filterEmployees = () => {\r\n    if (searchWord.value) {\r\n        currentPage.value = 1; // 重置为第一页\r\n    }\r\n    // 在所有群聊中搜索\r\n    const allGroups = [...grouplist_inner.value, ...grouplist_outter.value];\r\n    const filteredGroups = allGroups.filter(group =>\r\n        group.name.toLowerCase().includes(searchWord.value.toLowerCase())\r\n    );\r\n\r\n    // 根据当前选中的标签显示对应的群聊\r\n    switch (activeName.value) {\r\n        case 'first': // 全部\r\n            grouplist.value = filteredGroups;\r\n            totalGroups.value = grouplist.value.length\r\n            break;\r\n        case 'second': // 内部\r\n            grouplist.value = filteredGroups.filter(group => !group.chatType);\r\n            totalGroups.value = grouplist.value.length\r\n            break;\r\n        case 'third': // 外部\r\n            grouplist.value = filteredGroups.filter(group => group.chatType === 'customer');\r\n            totalGroups.value = grouplist.value.length\r\n            break;\r\n    }\r\n}\r\n\r\n\r\nonMounted(() => getgrouplist(1, 100, ''));\r\nwatch(searchWord, filterEmployees);\r\n//-------------------------------------------------------------------------- 点击导航标签\r\nconst handleClick = (tab) => {\r\n    console.log(\"检测到群聊标签被点击\", tab.props.name);\r\n    // 根据标签名称切换显示不同的群聊列表\r\n    switch (tab.props.name) {\r\n        case 'first': // 全部\r\n            grouplist.value = [...grouplist_inner.value, ...grouplist_outter.value];\r\n            totalGroups.value = grouplist.value.length\r\n            break;\r\n        case 'second': // 内部\r\n            grouplist.value = grouplist_inner.value;\r\n            totalGroups.value = grouplist.value.length\r\n            break;\r\n        case 'third': // 外部\r\n            grouplist.value = grouplist_outter.value;\r\n            totalGroups.value = grouplist.value.length\r\n            break;\r\n    }\r\n}\r\n// -------------------------------------------------------------------------- 点击群聊卡片\r\n//注入选中的会话\r\nconst selectedChat = inject('selectedChat', null);\r\n\r\n// 处理员工卡片点击事件\r\nconst handleGroupClick = (group) => {\r\n    let from = group.chatId //会话发起方id，如果 type = groupchat，为群聊ID；\r\n    let to = '' //会话接收方id，如果 type = groupchat，为 null；\r\n\r\n    let fromName = getGroupName(group) //会话发起方名称，如果 type = groupchat，为群聊名称；\r\n    let fromLable = '' //会话发起方标签，如果 type = groupchat，为null；\r\n    let fromAvatar = ''\r\n\r\n    let toName = '' //会话接收方名称，如果 type = groupchat，为 null；\r\n    let toAvatar = '' //会话接收方头像 ，如果 type = groupchat，为 null；\r\n    let toLable = '' //会话接收方类型 ，如果 type = groupchat，为 null,为employee时是员工的标签，默认是‘员工’；\r\n\r\n    let type = 'groupchat' //会话接收方类型，群聊 type = groupchat，\r\n    let page = 1 //拉取第一页数据\r\n    let limit = 100 //拉取30条会话\r\n\r\n    const selectedChat_info = {\r\n        from: from,\r\n        to: to,\r\n\r\n        fromName: fromName,\r\n        fromLable: fromLable,\r\n        fromAvatar: fromAvatar,\r\n\r\n        toName: toName,\r\n        toAvatar: toAvatar,\r\n        toLable: toLable,\r\n\r\n        type: type,//groupchat（群聊），其它一律视为非群聊\r\n        page: page,\r\n        limit: limit\r\n    }\r\n    selectedChat.value = selectedChat_info,\r\n\r\n        console.log('到某一群聊会话被点击', selectedChat.value);\r\n\r\n};\r\n\r\n\r\n\r\n// --------------------------------------------------------------------------控制群名显示\r\n\r\nconst getGroupName = (group) => {\r\n    if (group.name) {\r\n        return group.name\r\n    }\r\n    const groupId = group.chatId\r\n    const groupName = '群聊'\r\n    if (groupId && groupId.length > 6) {\r\n        group.name = groupName + groupId.slice(0, 9)+ '...'\r\n        return groupName + groupId.slice(0, 9) + '...'\r\n    }\r\n    return ''\r\n}\r\n\r\n// --------------------------------------------------------------------------基于ownerId获取ownerName\r\n// 键值存储已获取的人员的 id, 避免接口冗余调用\r\nconst owner_nameAndLable = ref({});\r\nconst owner_nameAndLable_cache = ref({});\r\n// 记录正在请求中的用户ID\r\nconst pending_requests = ref(new Set());\r\n\r\n// 添加一个ref来存储群主名称\r\nconst ownerNames = ref({})\r\n\r\nconst getOwnerName = async (from_userId) => {\r\n    // 如果已经有缓存的名称，直接返回\r\n    if (ownerNames.value[from_userId]) {\r\n        return ownerNames.value[from_userId];\r\n    }\r\n\r\n    // 如果已经在获取中或已有缓存，则不重复获取\r\n    if (pending_requests.value.has(from_userId) || owner_nameAndLable_cache.value[from_userId]) {\r\n        const cachedData = owner_nameAndLable_cache.value[from_userId];\r\n        ownerNames.value[from_userId] = cachedData?.from_user.name || '未知用户';\r\n        return ownerNames.value[from_userId];\r\n    }\r\n\r\n    try {\r\n        // 添加到正在请求的集合中\r\n        pending_requests.value.add(from_userId);\r\n\r\n        console.log('调用getOwnerName方法')\r\n        const jwt_token = localStorage.getItem('access_token');\r\n        const response = await axiosInstance.get('/api/chatmessage/detail/getNameAndLable', {\r\n            params: { from_userId },\r\n            headers: { Authorization: 'Bearer ' + jwt_token }\r\n        });\r\n        const data = response.data.data;\r\n        owner_nameAndLable.value[from_userId] = data;\r\n        owner_nameAndLable_cache.value[from_userId] = data;\r\n        ownerNames.value[from_userId] = data.from_user.name;\r\n        return ownerNames.value[from_userId];\r\n    } catch (error) {\r\n        console.error('获取群聊owner信息失败:', error);\r\n        owner_nameAndLable.value[from_userId] = { from_user: { name: '未知用户' } };\r\n        owner_nameAndLable_cache.value[from_userId] = { from_user: { name: '未知用户' } };\r\n        ownerNames.value[from_userId] = '未知用户';\r\n        return '未知用户';\r\n    } finally {\r\n        // 请求完成后从集合中移除\r\n        pending_requests.value.delete(from_userId);\r\n    }\r\n};\r\n\r\n// 键值存储已获取的人员的 id, 避免接口冗余调用\r\nconst goup_chatId = ref({});\r\nconst goup_chatId_cache = ref({});\r\n// 记录正在请求中的用户ID\r\nconst pending_requests1 = ref(new Set());\r\n\r\n\r\nconst getGroupMenberNumber = async (chatId) => {\r\n\r\n    // 如果已经在获取中或已有缓存，则不重复获取\r\n    if (pending_requests1.value.has(chatId)) {\r\n        return;\r\n    }\r\n    if (goup_chatId_cache.value[chatId]) {\r\n        const cachedData = goup_chatId_cache.value[chatId];\r\n        goup_chatId.value[chatId] = cachedData || 0;\r\n        return cachedData;\r\n    }\r\n\r\n    try {\r\n        // 添加到正在请求的集合中\r\n        pending_requests.value.add(chatId);\r\n\r\n        console.log('调用getGroupMenberNumber方法')\r\n\r\n        const jwt_token = localStorage.getItem('access_token');\r\n\r\n        const response = await axiosInstance.get('/api/chatmessage/chatter/getgroupMenberCount', {\r\n            params: { chatId },\r\n            headers: { Authorization: 'Bearer ' + jwt_token }\r\n        });\r\n        const data = response.data.data;\r\n        goup_chatId.value[chatId] = data;\r\n        goup_chatId_cache.value[chatId] = data;\r\n        goup_chatId.value[chatId] = data;\r\n        return goup_chatId.value[chatId];\r\n    } catch (error) {\r\n        console.error('获取群聊人数失败:', error);\r\n        goup_chatId.value[chatId] = 0;\r\n        goup_chatId_cache.value[chatId] = 0;\r\n        return 0;\r\n    } finally {\r\n        // 请求完成后从集合中移除\r\n        pending_requests1.value.delete(chatId);\r\n    }\r\n\r\n}\r\n\r\n</script>\r\n\r\n<style>\r\n.mainContainer-grouplist {\r\n    margin: 0;\r\n    height: calc(100vh - 6.969rem);\r\n    background-color: #ffffff;\r\n    display: flex;\r\n    flex-direction: column;\r\n    width: 15.625rem;\r\n    padding: 1rem 0.75rem;\r\n    border-right: 0.0625rem solid #e6e6e6;\r\n    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.02);\r\n}\r\n\r\n.search {\r\n    margin-bottom: 0.3125rem;\r\n}\r\n\r\n.search-input {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.search-input:hover .el-input__wrapper {\r\n    box-shadow: 0 0 0 0.0625rem #e6e6e6 inset;\r\n}\r\n\r\n.search-icon {\r\n    color: #909399;\r\n}\r\n\r\n.lable-navigator {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar {\r\n    width: 0.375rem;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar-thumb {\r\n    background-color: #e0e0e0;\r\n    border-radius: 0.1875rem;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar-track {\r\n    background-color: transparent;\r\n}\r\n\r\n.group-card {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    height: 4rem;\r\n    padding: 0 0.75rem;\r\n    margin: 0.25rem 0;\r\n    border-radius: 0.5rem;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.group-card:hover {\r\n    background-color: #f5f7fa;\r\n    transform: translateX(0.125rem);\r\n}\r\n\r\n.group-card .profile {\r\n    /* background: linear-gradient(135deg, #1890ff, #0960bd); */\r\n    /* color: #ffffff; */\r\n    width: 2.5rem;\r\n    height: 2.5rem;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    border-radius: 0.5rem;\r\n    margin-right: 0.75rem;\r\n    /* font-weight: 500; */\r\n    box-shadow: 0 0.125rem 0.25rem rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n.group-card .profile img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.group-card .info {\r\n    flex: 1;\r\n}\r\n\r\n.group-card .info .name {\r\n    font-size: 0.875rem;\r\n    font-weight: 500;\r\n    color: #2c3e50;\r\n    margin-bottom: 0.25rem;\r\n}\r\n\r\n.group-card .info .info-detail {\r\n    font-size: 0.83rem;\r\n    font-weight: 500;\r\n    color: #5f6974;\r\n\r\n    display: flex;\r\n    flex-direction: row;\r\n}\r\n\r\n.group-card .info .info-detail .owner {\r\n    margin-left: 0.3rem;\r\n}\r\n\r\n.pageContainer {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 0.75rem 0;\r\n    margin-top: 0.5rem;\r\n    border-top: 0.0625rem solid #f0f0f0;\r\n    width: 100%;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination) {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    padding: 0 0.5rem;\r\n    font-size: 0.8125rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pagination__total) {\r\n    min-width: auto;\r\n    margin-right: 0.5rem;\r\n    font-size: 0.8125rem;\r\n    color: #606266;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager) {\r\n    margin: 0;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager li) {\r\n    min-width: 1.5rem;\r\n    height: 1.5rem;\r\n    line-height: 1.5rem;\r\n    font-weight: normal;\r\n    margin: 0 0.125rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager li.active) {\r\n    background-color: #1890ff;\r\n    color: #ffffff;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination button) {\r\n    min-width: 1.5rem;\r\n    height: 1.5rem;\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .btn-prev),\r\n.pageContainer :deep(.el-pagination .btn-next) {\r\n    margin: 0 0.125rem;\r\n    background-color: #f4f4f5;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-icon) {\r\n    font-size: 0.75rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .more) {\r\n    background-color: transparent;\r\n}\r\n\r\n.grouplist-tabs {\r\n    height: 100%;\r\n}\r\n\r\n.grouplist-tabs :deep(.el-tabs__header) {\r\n    margin-bottom: 1rem;\r\n}\r\n\r\n.grouplist-tabs :deep(.el-tabs__nav-wrap::after) {\r\n    height: 0.0625rem;\r\n    background-color: #f0f0f0;\r\n}\r\n\r\n.grouplist-tabs :deep(.el-tabs__item) {\r\n    font-size: 0.875rem;\r\n    color: #606266;\r\n    padding: 0 1rem;\r\n}\r\n\r\n.grouplist-tabs :deep(.el-tabs__item.is-active) {\r\n    color: #1890ff;\r\n    font-weight: 500;\r\n}\r\n\r\n.grouplist-tabs :deep(.el-tabs__active-bar) {\r\n    background-color: #1890ff;\r\n    height: 0.125rem;\r\n}\r\n</style>\r\n", "import script from \"./GroupList.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./GroupList.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./GroupList.vue?vue&type=style&index=0&id=42f82507&lang=css\"\n\nconst __exports__ = script;\n\nexport default __exports__"], "names": ["searchWord", "ref", "activeName", "grouplist", "grouplist_inner", "grouplist_outter", "totalGroups", "currentPage", "pageSize", "currentComponent_List", "inject", "currentComponent_ChatList", "getgrouplist", "curPage", "maxPageNum", "searchName", "value", "jwt_token", "localStorage", "getItem", "axiosInstance", "post", "type", "page", "limit", "name", "headers", "Authorization", "then", "res", "data", "code", "console", "log", "result", "total", "filter", "group", "chatType", "for<PERSON>ach", "owner", "getOwnerName", "chatId", "getGroupMenberNumber", "catch", "error", "ElMessage", "handlePageChange", "filterEmployees", "allGroups", "filteredGroups", "toLowerCase", "includes", "length", "onMounted", "watch", "handleClick", "tab", "props", "selectedC<PERSON>", "handleGroupClick", "from", "to", "fromName", "getGroupName", "fromLable", "fromAvatar", "to<PERSON>ame", "<PERSON><PERSON><PERSON><PERSON>", "toLable", "selectedChat_info", "groupId", "groupName", "slice", "owner_nameAndLable", "owner_nameAndLable_cache", "pending_requests", "Set", "ownerNames", "async", "from_userId", "has", "cachedData", "from_user", "add", "response", "get", "params", "delete", "goup_chatId", "goup_chatId_cache", "pending_requests1", "__exports__"], "sourceRoot": ""}