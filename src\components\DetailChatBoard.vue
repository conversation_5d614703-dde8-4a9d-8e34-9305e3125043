<template>

    <div class="chat-board" :style="chatBoardStyle" v-if="chatBoardVisible">
        <div class="operate-area">
            <div class="detailChat-info" v-if="selectedChat.type == ''">
                <!-- <span class="sender">{{ whichDetailChat.sender }}</span> -->
                <span class="sender">{{ selectedChat.fromName }}</span>
                <span> 与 </span>
                <!-- <span class="receiver">{{ whichDetailChat.receiver }}</span> -->
                <span class="receiver">{{ selectedChat.toName }}</span>
                <span> 的会话记录</span>
            </div>
            <div class="detailChat-info" v-if="selectedChat.type == 'groupchat'">
                <!-- <span class="sender">{{ whichDetailChat.sender }}</span> -->
                <span class="sender">群聊 {{ selectedChat.fromName }} 的会话记录</span>
            </div>
            <div class="refresh-area">
                <el-icon size="18px" @click="refreshChat">
                    <Refresh />
                </el-icon>
            </div>
        </div>
        <!-- 顶部搜索区域 -->
        <div class="search-area">
            <!-- <el-tooltip content="搜索功能开发中，敬请期待" placement="top" effect="light"> -->
            <el-input v-model="searchName" placeholder="搜索聊天记录" class="search-input" clearable>
                <template #prefix>
                    <el-icon class="search-icon">
                        <Search />
                    </el-icon>
                </template>
            </el-input>
            <!-- </el-tooltip> -->

            <div class="filter-options">
                <el-tooltip content="功能开发中，敬请期待" placement="top" effect="light">
                    <el-button type="primary" plain size="small">
                        <el-icon>
                            <Filter />
                        </el-icon>
                        筛选条件
                    </el-button>
                </el-tooltip>
            </div>

            <div class="filter-type">
                <el-button
                    :type="filter_quick === 'text' ? 'primary' : 'primary'"
                    :plain="filter_quick !== 'text'"
                    size="small"
                    @click="handleFilterQuick('text')">
                    <el-icon>
                        <ChatDotRound />
                    </el-icon>
                    消息
                </el-button>
                <el-button
                    :type="filter_quick === 'file' ? 'primary' : 'primary'"
                    :plain="filter_quick !== 'file'"
                    size="small"
                    @click="handleFilterQuick('file')">
                    <el-icon>
                        <Document />
                    </el-icon>
                    文件
                </el-button>
                <el-button
                    :type="filter_quick === 'picture' ? 'primary' : 'primary'"
                    :plain="filter_quick !== 'picture'"
                    size="small"
                    @click="handleFilterQuick('picture')">
                    <el-icon>
                        <Picture />
                    </el-icon>
                    图片
                </el-button>
            </div>
            <!-- -->

        </div>

        <!-- 聊天内容区域 -->
        <div class="chat-content" ref="chatContentRef" @scroll="handleScroll"
            :style="{ pointerEvents: isRefreshing ? 'none' : 'auto' }">
            <!-- 添加遮罩层 -->
            <div class="content-overlay" v-if="isRefreshing" :style="{ top: overlayTop + 'px' }">
                <el-icon class="loading-icon" :size="30">
                    <Loading />
                </el-icon>
                <span class="loading-text">刷新中...</span>
            </div>
            <!-- 加载更多的提示 -->
            <div v-if="isLoading" class="loading-more">
                <el-icon class="loading-icon" :size="20">
                    <Loading />
                </el-icon>
                <span>加载更多消息...</span>
            </div>
            <div v-if="isProcessing">
                <div v-for="(message, index) in chatDetailMessages" :key="index">
                    <!-- 消息内容 -->

                    <div :class="['message-container', handleMessageType(message)]" v-if="message.action !== 'switch'">
                        <!-- 头像 -->
                        <!-- 头像、时间、具体消息内容的显示与否直接通过message.msgtype是否等于aggree、disagree控制 -->
                        <div class="avatar"
                            v-if="message.msgType !== 'agree' && message.msgType !== 'disagree' && message.msgType !== 'voiptext'">
                            <img :src="avatar_from_or_to(message)" alt="人员头像">
                        </div>

                        <!-- 消息气泡 -->
                        <div class="message-content"
                            v-if="message.msgType !== 'agree' && message.msgType !== 'disagree' && message.msgType !== 'voiptext'">
                            <div class="sender-info">
                                <span class="sender-name">{{ preprocessedNames.get(message.fromUser) }}</span>
                                <span class="sender-lable" :class="getLableClass(message)">{{ getLable(message)
                                }}</span>
                                <span class="message-time">{{ convertTime(message.msgTime) }}</span>
                            </div>
                            <!-- 此处区分不同消息类型 -->
                            <!-- 纯文本消息类型 -->
                            <div class="message-bubble text-content"
                                v-if="handleMessageContentType(message) === 'text'">
                                <p v-html="highlightText(messageContent[message.msgid]?.content || '', searchName)"></p>
                            </div>

                            <!-- 文件消息类型 -->
                            <div class="message-bubble file-content" v-if="handleMessageContentType(message) === 'file'"
                                @click="getFileContent(messageContent[message.msgid])">
                                <div class="left">
                                    <p class="file-name">{{ messageContent[message.msgid]?.filename }}</p>
                                    <p class="file-size">{{ formatFileSize(messageContent[message.msgid]?.filesize) }}
                                    </p>
                                </div>
                                <div class="right">
                                    <img :src="getFileIcon(messageContent[message.msgid]?.fileext)" alt="文件类型">
                                </div>
                            </div>

                            <!-- 图片消息类型  -->
                            <div class="message-bubble image-content"
                                v-if="handleMessageContentType(message) === 'image'" @click="handleImageClick(message)">
                                <img :src="getImageUrl(message)" alt="图片">
                            </div>

                            <!-- emotion消息类型 ：通过handleMessageContentType方法转换使用图片消息类型的模板-->

                            <!-- 语音类型消息 -->
                            <div class="message-bubble voice-content"
                                v-if="handleMessageContentType(message) === 'voice'">
                                <div class="voice-wrapper"
                                    @click="test(voiceUrls[messageContent[message.msgid]?.sdkfileid])">
                                    <av-waveform
                                        :src="voiceUrls[messageContent[message.msgid]?.sdkfileid]"></av-waveform>
                                </div>
                            </div>

                            <!-- 语音通话类型消息 -->
                            <div class="message-bubble meeting-voice-content"
                                v-if="handleMessageContentType(message) === 'meeting_voice_call'">
                                <div class="meeting-voice-header">
                                    <el-icon class="meeting-voice-icon">
                                        <Phone />
                                    </el-icon>
                                    <span class="meeting-voice-title">语音通话</span>
                                </div>
                                <div class="meeting-voice-wrapper"
                                    @click="test(voiceUrls[messageContent[message.msgid]?.sdkfileid])">
                                    <av-waveform
                                        :src="voiceUrls[messageContent[message.msgid]?.sdkfileid]"></av-waveform>
                                </div>
                            </div>

                            <!-- 视频消息类型  -->
                            <div class="message-bubble video-content"
                                v-if="handleMessageContentType(message) === 'video'">
                                <div class="video-container">
                                    <video controls class="video-player" :src="getVideoUrl(message)"
                                        @play="handleVideoPlay(message)" @pause="handleVideoPause(message)"
                                        @ended="handleVideoEnded(message)" @error="handleVideoError(message)"
                                        :ref="el => setVideoRef(message.msgid, el)">
                                        <span>您的浏览器不支持视频播放</span>
                                    </video>
                                </div>
                            </div>

                            <!-- 卡片/名片类型消息 -->
                            <div class="message-bubble card-content"
                                v-if="handleMessageContentType(message) === 'card'">
                                <div class="card-container">
                                    <div class="top">
                                        <div class="left">
                                            <div class="card-company">
                                                <span>{{ messageContent[message.msgid]?.corpname }}</span>
                                            </div>
                                            <div class="card-userId">
                                                <span>{{ getCardUserId(messageContent[message.msgid]?.userid) }}</span>
                                            </div>
                                            <div class="card-Name">
                                                <span>{{ preprocessedNames.get(message.fromUser) }}</span>
                                            </div>
                                        </div>
                                        <div class="right">
                                            <div class="card-avatar">
                                                <img :src="avatar_from_or_to(message)" alt="卡片头像">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bottom">
                                        <span>个人名片</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 链接消息 -->
                            <div class="message-bubble link-content"
                                v-if="handleMessageContentType(message) === 'link'">
                                <div class="link-card-container"
                                    @click="handleLinkClick(messageContent[message.msgid]?.link_url)">
                                    <div class="link-image-container">
                                        <img :src="messageContent[message.msgid]?.image_url" alt="链接图片"
                                            class="link-image">
                                    </div>
                                    <div class="link-content">
                                        <div class="link-title" :class="hasSpecialSymbol ? 'title-with-symbol' : ''">
                                            {{ messageContent[message.msgid]?.title }}
                                        </div>
                                        <div class="link-description">
                                            {{ messageContent[message.msgid]?.description }}
                                        </div>
                                        <div class="link-url">
                                            <span class="url-text">{{
                                                truncateUrl(messageContent[message.msgid]?.link_url)
                                            }}</span>
                                            <span class="icon-external-link">→</span>
                                        </div>
                                    </div>
                                </div>
                            </div>



                        </div>

                        <div class="recall-lable" v-if="message.msgType === 'revoke'">
                            <el-icon>
                                <RefreshLeft />
                            </el-icon>
                            <span>撤回消息</span>
                        </div>

                        <!-- 同意会话聊天内容 -->
                        <div class="agree" v-if="handleMessageContentType(message) === 'agree'">
                            <div class="agree-time">{{ convertTime(messageContent[message.msgid]?.agree_time) }}</div>
                            <p>客户同意会话存档</p>
                        </div>

                        <!-- 不同意会话聊天内容 -->
                        <div class="disagree" v-if="handleMessageContentType(message) === 'disagree'">
                            <div class="disagree-time">{{ convertTime(messageContent[message.msgid]?.disagree_time) }}
                            </div>
                            <p>客户不同意会话存档</p>
                        </div>

                    </div>

                    <!-- 切换企业日志 -->
                    <div class="switch-label" v-if="message.action === 'switch'">
                        <div class="switch-time">{{ convertTime(message.time) }}</div>
                        <p>{{ message.user }} 切换了企业</p>
                    </div>



                </div>
            </div>

        </div>
    </div>

    <div class="chat-board-nonShow" :style="chatBoardStyle" v-if="!chatBoardVisible">
        <h3 class="non-picked">未选择查看任何会话</h3>
        <img src="../assets/会话详情面板空页.png" alt="会话详情面板空页">
    </div>
    <!-- 添加聊天记录弹窗组件 -->
    <ChatRecordPopUp v-model:visible="chatRecordVisible" :title="currentChatRecord?.title"
        :chat-records="currentChatRecord?.item || []" @close="chatRecordVisible = false" />

    <!-- 添加图片预览遮罩层 -->
    <div class="image-preview-mask" v-if="imagePreviewVisible" @click="closeImagePreview">
        <div class="image-preview-container" @click.stop @wheel.prevent="handleWheel">
            <img :src="previewImageUrl" alt="预览图片" class="preview-image" :style="{
                width: `${baseWidth * scale}px`,
                height: `${baseHeight * scale}px`,
                minWidth: '100px',
                minHeight: '100px'
            }" @load="handleImageLoad">
        </div>
    </div>

    <!-- 文件操作弹窗 -->
    <el-dialog v-model="fileDialogVisible" title="文件操作" width="30%" :show-close="true" :close-on-click-modal="true"
        :close-on-press-escape="true" class="file-dialog">
        <div class="file-dialog-content">
            <div class="file-info">
                <img :src="getFileIcon(currentFile?.fileext)" alt="文件类型" class="file-icon">
                <div class="file-details">
                    <p class="file-name">{{ currentFile?.filename }}</p>
                    <p class="file-size">{{ formatFileSize(currentFile?.filesize) }}</p>
                </div>
            </div>
            <el-alert title="当前文件预览只支持docx、xlsx、pdf格式文件" type="info" :closable="false" show-icon
                style="margin-bottom: 20px;" />
            <div class="file-actions">
                <el-button type="primary" @click="previewFile"
                    :disabled="!['docx', 'xlsx', 'pdf'].includes(currentFile?.fileext?.toLowerCase())">
                    预览
                </el-button>
                <el-button type="success" @click="downloadFile">下载</el-button>
            </div>
        </div>
    </el-dialog>

    <!-- 文件预览弹窗 -->
    <el-dialog v-model="previewDialogVisible" :title="currentFile?.filename" width="80%" :show-close="true"
        :close-on-click-modal="false" :close-on-press-escape="true" class="preview-dialog">
        <div class="preview-container">
            <!-- docx文件预览 -->
            <vue-office-docx v-if="currentFile?.fileext?.toLowerCase() === 'docx'" :src="currentFileUrl"
                @rendered="handleDocxRendered" @error="handlePreviewError" />
            <!-- excel文件预览 -->
            <vue-office-excel v-if="currentFile?.fileext?.toLowerCase() === 'xlsx'" :src="currentFileUrl"
                @rendered="handleExcelRendered" @error="handlePreviewError" />
            <!-- pdf文件预览 -->
            <vue-office-pdf v-if="currentFile?.fileext?.toLowerCase() === 'pdf'" :src="currentFileUrl"
                @rendered="handlePdfRendered" @error="handlePreviewError" />
        </div>
    </el-dialog>
</template>

<script setup>
import { Refresh, RefreshLeft, Loading } from '@element-plus/icons-vue'
import { ref, inject, watch, nextTick, onUnmounted, computed } from 'vue'
// , Clock, Location, User, CollectionTag, Microphone
import { Search, Filter, Document, Picture, ChatDotRound, Phone } from '@element-plus/icons-vue'
import ChatRecordPopUp from './ChatRecordPopUp.vue'
import axiosInstance from '@/axiosConfig.js'; // 引入 axios 实例
import { ElMessage } from 'element-plus';


//文件在线预览方案
import VueOfficeDocx from '@vue-office/docx';
import VueOfficeExcel from '@vue-office/excel';
import VueOfficePdf from '@vue-office/pdf';

import BenzAMRRecorder from 'benz-amr-recorder';


// 将 AudioBuffer 转换为 WAV Blob 的辅助函数
const audioBufferToWavBlob = (audioBuffer) => {
    const numberOfChannels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;
    const length = audioBuffer.length;
    const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
    const view = new DataView(arrayBuffer);

    // WAV 文件头
    const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i));
        }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * numberOfChannels * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * 2, true);
    view.setUint16(32, numberOfChannels * 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * numberOfChannels * 2, true);

    // 写入音频数据
    let offset = 44;
    for (let i = 0; i < length; i++) {
        for (let channel = 0; channel < numberOfChannels; channel++) {
            const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
            view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
            offset += 2;
        }
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' });
};

// 搜索关键词
const searchName = ref('')

//----------------------------------------- chat-board显示以及会话详情数据获取 ----------------------------------------


// 获取会话详情接口的参数信息,调用接口获取会话详情数据
const selectedChat = inject('selectedChat', ref(null))

const chatDetailMessages = ref([]) //存储详情会话数组

const chatDetailMessagesTotal = ref(null)//记录获取到的详情会话总数

const fetchedPages = ref(new Set());//记录已经获取过的页码

const mediaLoadingCount = ref(0); // 添加媒体加载计数器
const totalMediaCount = ref(0);   // 添加媒体总数计数器

const filter_quick = ref(null) // 快速过滤

// 处理快速筛选
const handleFilterQuick = (type) => {
    // 如果点击的是当前已选中的类型，则取消选择
    if (filter_quick.value === type) {
        filter_quick.value = null
    } else {
        filter_quick.value = type
    }
    // 重新获取聊天记录
    // 清空已获取的页码，重新从第一页开始获取
    fetchedPages.value.clear()
    chatDetailMessages.value = []
    page.value = 1
    getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, searchName.value, page.value, limit.value)
}

// 调用获取会话详情接口
const getDetailChatMessages = (from, to, type, searchName, page, limit) => {

    console.log("调用获取会话详情接口")


    if (fetchedPages.value.has(page)) {
        return;//如果该页数据已经获取过，直接返回
    }

    const jwt_token = localStorage.getItem('access_token')
    // 构建请求参数
    const params = {
        from: from,
        to: to,
        type: type || '',  // 确保type不为undefined
        searchName: searchName,
        page: page,
        limit: limit,
        filter_quick: filter_quick.value
    }
    // 只有当searchName有值且不为空字符串时才添加到参数中
    // if (searchName && searchName.trim() !== '') {
    //     params.searchName = searchName.trim()
    // }
    axiosInstance.post('/api/chatmessage/detail', params, {
        headers: { Authorization: 'Bearer ' + jwt_token }
    }).then(res => {
        const newMessages = res.data.data.data

        // 重置媒体计数器
        mediaLoadingCount.value = 0;
        totalMediaCount.value = 0;

        // 计算需要加载的媒体总数
        newMessages.forEach(message => {
            if (message.msgType === 'image' ||
                message.msgType === 'emotion' ||
                message.msgType === 'voice' ||
                message.msgType === 'video' ||
                message.msgType === 'file' ||
                message.msgType === 'meeting_voice_call') {
                totalMediaCount.value++;
            }
        });

        // 检查chatDetailMessages.value是否为空数组
        if (chatDetailMessages.value.length === 0) {
            chatDetailMessages.value = newMessages.sort((a, b) => {
                return a.msgTime - b.msgTime;
            });
            // 只有在没有媒体需要加载时才立即滚动
            if (totalMediaCount.value === 0) {
                scrollToBottom();
            }
        } else {
            chatDetailMessages.value = [...chatDetailMessages.value, ...newMessages].sort((a, b) => {
                return a.msgTime - b.msgTime;
            });
        }

        chatDetailMessagesTotal.value = newMessages.length

        //记录已经获取过的页码
        fetchedPages.value.add(page);

    }).catch(error => {
        console.log(error);
        ElMessage.error('获取会话详情失败，请检查网络或联系管理员');
    }).finally(
    );
}

// ------------------------------------------------------------------------------------------- Utils
// ----------------------------------------------------- 显示相关
//控制chat-board的显示
const chatBoardVisible = ref(false)

const old_selectedChat = ref(null)

watch(selectedChat, (newValue) => {
    console.log('DetailChatBoard检测到selectedChat存在变化', newValue);

    if (newValue) {
        currentPage.value = 1
        chatBoardVisible.value = true

        chatDetailMessages.value = [] //清空消息数组
        fetchedPages.value = new Set() //清空已获取页码的记录
        searchName.value = ''
        hasMore.value = true
        old_selectedChat.value = newValue

        getDetailChatMessages(newValue.from, newValue.to, newValue.type, searchName.value, newValue.page, newValue.limit)
    }
    else {
        chatBoardVisible.value = false
        // console.log('selectedChat为空');
    }

})

// 监听搜索关键词变化
watch(searchName, (newValue) => {
    if ((selectedChat.value && newValue != "" && newValue.length != 0 && newValue != null) || (old_selectedChat.value == selectedChat.value)) {
        currentPage.value = 1
        chatDetailMessages.value = [] //清空消息数组
        fetchedPages.value = new Set() //清空已获取页码的记录
        hasMore.value = true
        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, newValue, selectedChat.value.page, selectedChat.value.limit)
    }
})

// 控制使用sent还是receive样式
const sender_or_reciever = (chatMessage) => {
    //当为群聊时，selectedChat.value.from=群聊id，selectedChat.value.to=''
    if (chatMessage.fromUser == selectedChat.value.from) {
        return 'sent'
    } else if (chatMessage.fromUser == selectedChat.value.to) {
        return 'received'
    }
    return 'received' //群聊默认
}

//控制使用员工头像还是用户头像
const avatar_from_or_to = (chatMessage) => {

    if (chatMessage.fromUser == selectedChat.value.from) {
        return selectedChat.value.fromAvatar
    } else if (chatMessage.fromUser == selectedChat.value.to) {
        return selectedChat.value.toAvatar
    }
    return require('@/assets/人员头像.png') //目前客户会话功能暂时无法拉取客户头像，所以会话详情面板界面展示的头像均使用静态图
}

//控制人员标签颜色 
const getLableClass = (chatMessage) => {
    if (chatMessage.fromUser == selectedChat.value.from) {
        return 'employee'
    } else if (chatMessage.fromUser == selectedChat.value.to) {
        if (selectedChat.value.toType == 1) {
            return 'wechat'
        } else {
            return 'other'
        }
    } else {
        if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.externalUserId) {
            const type = sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.type
            switch (type) {
                case 1:
                    return 'wechat';
                case 2:
                    return 'other';
                default:
                    return 'other'
            }
        } else if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.userid) {
            return 'employee';
        }
        return 'other'
    }


}

//控制人员标签显示内容
const getLable = (chatMessage) => {
    if (chatMessage.fromUser == selectedChat.value.from) {
        return selectedChat.value.fromLable
    } else if (chatMessage.fromUser == selectedChat.value.to) {
        return selectedChat.value.toLable
    } else {
        if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.externalUserId) {
            const type = sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.type
            switch (type) {
                case 1:
                    return '@微信';
                case 2:
                    return sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.corpName ? `@${sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.corpName}` : '@未知企业';
                default:
                    return ''
            }
        } else if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.userid) {
            return '@员工';
        }
        return ''
    }

}

//处理显示的发送方、接收方名称
// const handle_sender_reciever_name = (fromName) => {
//     if (fromName == selectedChat.value.from) {
//         return selectedChat.value.fromName
//     } else if (fromName == selectedChat.value.to) {
//         return selectedChat.value.toName
//     } else {

//         // 先检查缓存中是否已有该用户的名称
//         if (sender_nameAndLable.value[fromName]) {
//             return sender_nameAndLable.value[fromName]?.from_user.name || '加载中...';
//         }
//         // 如果缓存中没有且不在请求中，则调用接口获取
//         if (!pending_requests.value.has(fromName)) {
//             get_sender_nameAndLable(fromName);
//         }
//         return '加载中...';
//     }
// }

//使用计算属性优化handle_sender_reciever_name方法反复调用
const preprocessedNames = computed(() => {
    // console.log('处理显示的发送方、接收方名称，chatmessages数组的长度为：', chatDetailMessages.value.length)
    const namesMap = new Map();
    chatDetailMessages.value.forEach(message => {
        const fromName = message.fromUser
        if (namesMap.has(fromName)) return;
        if (fromName == selectedChat.value.from) {
            namesMap.set(fromName, selectedChat.value.fromName)
        } else if (fromName == selectedChat.value.to) {
            namesMap.set(fromName, selectedChat.value.toName)
        } else {
            namesMap.set(fromName,
                sender_nameAndLable_cache.value[fromName]?.from_user.name || (checkAndFetchName(fromName) ? '加载中...' : '未知用户')
            )
        }
    });
    return namesMap;
})

//提取判断逻辑
const checkAndFetchName = (fromUser) => {
    if (!sender_nameAndLable.value[fromUser] && !pending_requests.value.has(fromUser)) {
        get_sender_nameAndLable(fromUser);
        return true;
    }
    return false;
}


// 键值存储已获取的人员的 id, 避免接口冗余调用
const sender_nameAndLable = ref({});
const sender_nameAndLable_cache = ref({});
// 记录正在请求中的用户ID
const pending_requests = ref(new Set());

//一般情况下只有选中群聊会话才会调用此方法
const get_sender_nameAndLable = (from_userId) => {
    // 如果已经在获取中或已有缓存，则不重复获取
    if (pending_requests.value.has(from_userId) || sender_nameAndLable_cache.value[from_userId]) {
        return;
    }


    // 添加到正在请求的集合中
    pending_requests.value.add(from_userId);

    // console.log('调用真实名称、标签获取接口')
    const jwt_token = localStorage.getItem('access_token');
    axiosInstance.get('/api/chatmessage/detail/getNameAndLable', {
        params: { from_userId },
        headers: { Authorization: 'Bearer ' + jwt_token }
    }).then(res => {
        if (res.data.code === 0) {
            const data = res.data.data;
            sender_nameAndLable.value[from_userId] = data;
            sender_nameAndLable_cache.value[from_userId] = data;
        }
    }).catch(error => {
        console.error('获取真实人员信息失败:', error);
        sender_nameAndLable.value[from_userId] = { from_user: { name: '未知用户' } };
        sender_nameAndLable_cache.value[from_userId] = { from_user: { name: '未知用户' } };
    }).finally(
        // 请求完成后从集合中移除
        pending_requests.value.delete(from_userId)

    );

}

//将时间戳转换为时间格式
const convertTime = (timestamp) => {
    if (!timestamp) return '';

    // 判断时间戳是否为13位，如果是10位则转换为13位
    const ts = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;

    const date = new Date(ts);

    // 获取年月日时分秒
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');

    // 拼接成指定格式
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

//-----------------------------------------------------------------------------------------------------文件消息
const fileUrls = ref([''])
const fileUrls_cache = ref([''])
const currentFile = ref(null)
const currentFileUrl = ref('')


// 添加文件操作相关的状态变量
const fileDialogVisible = ref(false)

// 根据文件扩展名获取对应的图标
const getFileIcon = (fileext) => {
    const iconMap = {
        // 文档类
        'doc': require('@/assets/文件类型图片/docx.png'),
        'docx': require('@/assets/文件类型图片/docx.png'),
        'pdf': require('@/assets/文件类型图片/pdf.png'),
        'txt': require('@/assets/文件类型图片/txt.png'),
        // 表格类
        'xls': require('@/assets/文件类型图片/xlsx.png'),
        'xlsx': require('@/assets/文件类型图片/xlsx.png'),
        // 演示类
        'ppt': require('@/assets/文件类型图片/ppt.png'),
        'pptx': require('@/assets/文件类型图片/ppt.png'),
        // 压缩包类
        'zip': require('@/assets/文件类型图片/zip.png'),
        'rar': require('@/assets/文件类型图片/zip.png'),
        '7z': require('@/assets/文件类型图片/zip.png'),
        // 图片类
        'jpg': require('@/assets/文件类型图片/jpg.png'),
        'jpeg': require('@/assets/文件类型图片/jpg.png'),
        'png': require('@/assets/文件类型图片/jpg.png'),
        'gif': require('@/assets/文件类型图片/jpg.png'),
        // 视频类
        'mp4': require('@/assets/文件类型图片/mp4.png'),
        'avi': require('@/assets/文件类型图片/mp4.png'),
        'mov': require('@/assets/文件类型图片/mp4.png'),
        // 音频类
        'mp3': require('@/assets/文件类型图片/mp3.png'),
        'wav': require('@/assets/文件类型图片/mp3.png'),
    }


    // 返回对应的图标，如果没有匹配则返回默认图标
    return iconMap[fileext] || require('@/assets/文件类型图片/default.png')
}

// 格式化文件大小
const formatFileSize = (size) => {
    if (size < 1024) {
        return size + 'B'
    } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + 'KB'
    } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(2) + 'MB'
    } else {
        return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
    }
}

const getFileContent = (message) => {
    // console.log('点击',message.sdkfileid)


    const sdkfileid = message.sdkfileid
    // console.log('sdkfileid:', sdkfileid)
    const url = fileUrls.value[sdkfileid]
    if (url) {
        currentFileUrl.value = url
        currentFile.value = message
        fileDialogVisible.value = true
    }
}

const previewDialogVisible = ref(false)

// 修改预览文件方法
const previewFile = () => {
    if (currentFileUrl.value && currentFile.value) {
        const fileExt = currentFile.value.fileext?.toLowerCase()
        if (['docx', 'xlsx', 'pdf'].includes(fileExt)) {
            previewDialogVisible.value = true
            fileDialogVisible.value = false
        }
    }
}

// 添加预览组件的回调方法
const handleDocxRendered = () => {
    console.log('docx渲染完成')
}

const handleExcelRendered = () => {
    console.log('excel渲染完成')
}

const handlePdfRendered = () => {
    console.log('pdf渲染完成')
}

const handlePreviewError = (error) => {
    console.error('预览出错:', error)
    ElMessage.error('文件预览失败，请尝试下载后查看')
    previewDialogVisible.value = false
}

// 下载文件
const downloadFile = () => {
    if (currentFileUrl.value && currentFile.value) {
        const link = document.createElement('a')
        link.href = currentFileUrl.value
        link.download = currentFile.value.filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    }
    fileDialogVisible.value = false
}


// ----------------------------------------------------------------------------------------语音消息
//定义一个变量用于存储语音
const voiceUrls = ref([''])
const voiceUrls_cache = ref([''])

// 语音播放状态管理
const voiceStates = ref({})
const currentPlayingVoice = ref(null)



const test = (url) => {
    console.log('test', url)
}











// ----------------------------------------------------------------------------------------图片消息
//定义一个变量用于存储图片url
const imageUrls = ref([''])
const imageUrls_cache = ref([''])

const getImageUrl = (message) => {
    const sdkfileid = get_sdkfileid(message)
    return imageUrls.value[sdkfileid]

}

// ----------------------------------------------------------------------------------------视频消息
const videoUrls = ref([''])
const videoUrls_cache = ref([''])

// 添加视频相关的状态管理
const videoRefs = ref({});  // 存储视频元素引用
const videoStates = ref({}); // 存储视频状态

// 设置视频引用
const setVideoRef = (msgid, el) => {
    if (el) {
        videoRefs.value[msgid] = el;
    }
};

// 处理视频播放
const handleVideoPlay = (message) => {
    try {
        const currentVideo = videoRefs.value[message.msgid];
        if (!currentVideo) return;

        // 暂停其他正在播放的视频
        Object.entries(videoRefs.value).forEach(([msgid, video]) => {
            if (msgid !== message.msgid && !video.paused) {
                video.pause();
            }
        });

        // 更新当前视频状态
        videoStates.value[message.msgid] = 'playing';
        console.log('视频开始播放:', message.msgid);
    } catch (error) {
        console.error('视频播放处理失败:', error);
    }
};

// 处理视频暂停
const handleVideoPause = (message) => {
    try {
        videoStates.value[message.msgid] = 'paused';
        console.log('视频暂停:', message.msgid);
    } catch (error) {
        console.error('视频暂停处理失败:', error);
    }
};

// 处理视频播放结束
const handleVideoEnded = (message) => {
    try {
        videoStates.value[message.msgid] = 'ended';
        console.log('视频播放结束:', message.msgid);
    } catch (error) {
        console.error('视频结束处理失败:', error);
    }
};

// 处理视频错误
const handleVideoError = (message) => {
    try {
        const video = videoRefs.value[message?.msgid];
        if (!video) return;

        console.error('视频加载失败:', video.error);
        videoStates.value[message?.msgid] = 'error';

        // 尝试重新加载视频
        const sdkfileid = get_sdkfileid(message);
        if (videoUrls_cache.value[sdkfileid]) {
            console.log('尝试从缓存重新加载视频');
            video.src = videoUrls_cache.value[sdkfileid];
            video.load();
        }

        // ElMessage.error('视频加载失败，请重试');
    } catch (error) {
        console.error('视频错误处理失败:', error);
    }
};

// 获取视频URL的方法
const getVideoUrl = (message) => {
    console.log('获取视频url', message)
    try {
        const sdkfileid = get_sdkfileid(message);
        const url = videoUrls.value[sdkfileid];

        // 如果URL不存在但有缓存，使用缓存
        if (!url && videoUrls_cache.value[sdkfileid]) {
            return videoUrls_cache.value[sdkfileid];
        }

        return url || '';
    } catch (error) {
        console.error('获取视频URL失败:', error);
        return '';
    }
};

// 在组件卸载时清理资源
onUnmounted(() => {
    // 清理视频URL
    Object.values(videoUrls.value).forEach(url => {
        if (url && url.startsWith('blob:')) {
            URL.revokeObjectURL(url);
        }
    });

    // 清理语音URL
    Object.values(voiceUrls.value).forEach(url => {
        if (url && url.startsWith('blob:')) {
            URL.revokeObjectURL(url);
        }
    });

    // 清理图片URL
    Object.values(imageUrls.value).forEach(url => {
        if (url && url.startsWith('blob:')) {
            URL.revokeObjectURL(url);
        }
    });

    // 清理视频状态
    videoStates.value = {};
    videoRefs.value = {};

    // 清理语音状态
    voiceStates.value = {};
    currentPlayingVoice.value = null;
});

// ----------------------------------------------------------------------------------------名片消息
const getCardUserId = (id) => {
    if (id && id.length > 20) {
        return id.slice(0, 20) + '...'
    }
    return id
}

// ----------------------------------------------------------------------------------------链接消息
// 处理链接点击事件
const handleLinkClick = (link_url) => {
    if (link_url) {
        window.open(link_url, '_blank');
    }
};

// 截断URL显示
const truncateUrl = (url) => {
    if (!url) return '';
    const baseUrl = new URL(url);
    let displayUrl = baseUrl.host;
    const path = baseUrl.pathname;

    if (path.length > 10) {
        displayUrl += path.substring(0, 10) + '...';
    } else {
        displayUrl += path;
    }

    return displayUrl;
};



// ----------------------------------------------------- 功能相关
//刷新按钮
// 添加刷新状态控制
const isRefreshing = ref(false)

const overlayTop = ref(0) // 遮罩层距离顶部的距离

// 计算超出可视区域的高度（scrollHeight - clientHeight）
const calculateOverlayTop = () => {
    if (!chatContentRef.value) return;
    if (chatContentRef.value) {
        const scrollHeight = chatContentRef.value.scrollHeight;
        const clientHeight = chatContentRef.value.clientHeight;
        overlayTop.value = scrollHeight - clientHeight;
    }
}

const refreshChat = async () => {
    if (isRefreshing.value) return; // 防止重复点击
    calculateOverlayTop();

    try {
        isRefreshing.value = true;
        const jwt_token = localStorage.getItem('access_token');
        currentPage.value = 1
        searchName.value = ''

        // 调用刷新接口
        const response = await axiosInstance.post('/api/wechat/chatdata/download', {
            seq: 0,
            limit: 200,
            proxy: "",
            password: "",
            timeout: 30,
            type: "AUTO_MODE"
        }, {
            headers: { Authorization: 'Bearer ' + jwt_token }
        });


        if (response.data.code !== 0) {
            ElMessage.error(response.data.msg || '刷新失败');
            return;
        }

        ElMessage.success('刷新成功');

    } catch (error) {
        console.error('刷新失败:', error);
        ElMessage.error('刷新失败，请检查网络或联系管理员');
    } finally {
        searchName.value = ''
        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, '', selectedChat.value.page, selectedChat.value.limit)
        // 请求后等待5秒
        await new Promise(resolve => setTimeout(resolve, 2000));
        isRefreshing.value = false;
    }
}

// ---------------------------------------------------------------------------------------------------- 消息处理
//消息位置：如果消息类型是同意会话类型，显示在中间；
// 如果是其他消息类型，需判断发送/接收方，发送方显示在右边，反之
const handleMessageType = (message) => {
    if (message.msgtype === 'agree' || message.msgtype === 'disagree') {
        return message.msgtype
    }
    else {
        return sender_or_reciever(message)
    }
}


//处理消息内容类型，若为撤回消息，可以通过此方法读取pre_msgid，从而进一步获取被撤回的消息，调用相应的渲染方法
const handleMessageContentType = (message) => {
    if (message.msgType == 'revoke') {
        // 获取chatMsg中的pre_msgid
        if (!message.chatMsg) {
            console.log('检查 message.chatMsg 是否存在: 该字段不存在');
            return " ";
        }
        const ChatMsg = JSON.parse(message.chatMsg);
        if (!ChatMsg || !ChatMsg.msgtype) {
            console.log('解析后的 ChatMsg 对象无效或 msgtype 字段不存在');
            return " ";
        }

        // 如果缓存中已有该消息，直接返回
        if (recall_msg_cache.value[ChatMsg.msgid]) {
            return recall_msg_cache.value[ChatMsg.msgid].msgType;
        }

        return "text"; // 默认返回text类型
    } else if (message.msgType === 'emotion') {
        return 'image'
    } else if (message.msgType === 'video') {
        return 'video'
    }
    else {
        return message.msgType
    }
}

//处理消息内容，若为撤回消息，可以通过此方法读取premise_id，从而获取被撤回的消息内容类型，调用相应的渲染方法
const messageContent = ref([])
const handleMessageContent = (message) => {
    const msgid = message.msgid
    try {
        //判断message.msgType是不是revoke,如果是的话message替换成从recall_msg中message.msgid对应的值
        if (message.msgType === 'revoke' && recall_msg.value[msgid]) {
            console.log('替换撤回消息内容');
            message = recall_msg.value[msgid];
        }

        // 检查 message 对象中 chatMsg 字段是否存在
        if (!message.chatMsg) {
            console.log('检查 message.chatMsg 是否存在: 该字段不存在');
            return;
        }

        // 若 chatMsg 字段存在，尝试将其从字符串解析为 JSON 对象
        const ChatMsg = JSON.parse(message.chatMsg);

        // console.log('解析后的 ChatMsg 对象:', ChatMsg);

        // 检查解析后的 ChatMsg 对象是否有效，以及其内部的 msgtype 字段是否存在
        if (!ChatMsg || !ChatMsg.msgtype) {
            console.log('解析后的 ChatMsg 对象无效或 msgtype 字段不存在');
            return;
        }

        //根据不同消息类型，返回消息信息
        switch (ChatMsg.msgtype) {
            case 'text':
                // return ChatMsg.text || " ";
                messageContent.value[msgid] = ChatMsg.text;
                // console.log('text100:',messageContent[message.msgid].content)
                break;
            case 'file':
                // return ChatMsg.file || " ";
                messageContent.value[msgid] = ChatMsg.file;
                break;
            case 'image':
            case 'emotion':
                break;
            case 'agree':
                // return ChatMsg.agree || " ";
                messageContent.value[msgid] = ChatMsg.agree;
                break;
            case 'disagree':
                // return ChatMsg.disagree || " ";
                messageContent.value[msgid] = ChatMsg.disagree;
                break;
            case 'video':
                // return ChatMsg.video || " ";
                messageContent.value[msgid] = ChatMsg.video;
                console.log('解析后的 ChatMsg 对象:', ChatMsg.video);
                break;
            case 'voice':
                // return ChatMsg.voice || " ";
                messageContent.value[msgid] = ChatMsg.voice;
                console.log('解析后的 ChatMsg 对象:', ChatMsg.voice);
                break;
            case 'card':
                // return ChatMsg.card || " ";
                messageContent.value[msgid] = ChatMsg.card;
                break;
            case 'link':
                // return ChatMsg.link || " ";
                messageContent.value[msgid] = ChatMsg.link;
                break;
            case 'meeting_voice_call':
                // return ChatMsg.meeting_voice_call || " ";
                messageContent.value[msgid] = ChatMsg.meeting_voice_call;
                break;
            default:
                break;
        }
    } catch (error) {
        console.error('解析 chatMsg 时出错:', error);
        return;
    }
}

// --------------------------------------------------------- 撤回消息相关处理

//定义一个变量用于存储撤回消息
const recall_msg = ref({})  // 修改为对象类型，使用 pre_msgid 作为 key
const recall_msg_cache = ref({}) // 新增缓存对象

// 获取撤回消息的方法
const fetchRecallMessage = (message) => {
    // console.log('调用获取撤回消息方法')
    if (!message.chatMsg) return;

    const ChatMsg = JSON.parse(message.chatMsg);
    if (!ChatMsg || !ChatMsg.msgtype || !ChatMsg.revoke) return;

    const pre_msgid = ChatMsg.revoke.pre_msgid;//撤回原消息的id
    const msgid = ChatMsg.msgid;//撤回消息的id
    // console.log('撤回消息的id', msgid)

    // 如果缓存中已有该消息，直接返回
    if (recall_msg_cache.value[msgid]) {
        recall_msg.value[msgid] = recall_msg_cache.value[msgid];
        return;
    }

    const jwt_token = localStorage.getItem('access_token');
    axiosInstance.get('/api/chatmessage/revoke_premsg', {
        params: { pre_msgid },
        headers: { Authorization: 'Bearer ' + jwt_token }
    }).then(res => {
        // console.log('获取到撤回原消息：', res.data.data)
        if (res.data.code === 0) {

            recall_msg_cache.value[msgid] = res.data.data;
            recall_msg.value[msgid] = res.data.data;
            console.log('写入撤回消息数组,写入msgid：', msgid)
            handleMessageContent(message)
        } else {
            ElMessage.error(res.data.msg || '获取撤回消息失败');
        }
    }).catch(error => {
        console.log(error)
    }).finally(
    );

}


// --------------------------------------------------------- 图片，表情、语音、视频消息相关处理

//获取媒体消息的sdkfileid
const get_sdkfileid = (message) => {
    if (!message?.chatMsg) return;

    const ChatMsg = JSON.parse(message.chatMsg);
    if (!ChatMsg || !ChatMsg.msgtype) return;

    let sdkfileid = '';

    // 检查消息类型和对应的属性是否存在
    if (ChatMsg.msgtype === 'image' && ChatMsg.image && ChatMsg.image.sdkfileid) {
        sdkfileid = ChatMsg.image.sdkfileid;
    } else if (ChatMsg.msgtype === 'emotion' && ChatMsg.emotion && ChatMsg.emotion.sdkfileid) {
        sdkfileid = ChatMsg.emotion.sdkfileid;
    } else if (ChatMsg.msgtype === 'voice') {
        sdkfileid = ChatMsg.voice.sdkfileid;
        console.log('获取语音 sdkfileid:', sdkfileid, 'ChatMsg.voice:', ChatMsg.voice);
    } else if (ChatMsg.msgtype === 'video') {
        sdkfileid = ChatMsg.video.sdkfileid;
    } else if (ChatMsg.msgtype === 'file') {
        sdkfileid = ChatMsg.file.sdkfileid;
    } else if (ChatMsg.msgtype === 'meeting_voice_call') {
        sdkfileid = ChatMsg.meeting_voice_call.sdkfileid;
        console.log('获取语音通话 sdkfileid:', sdkfileid, 'ChatMsg.meeting_voice_call:', ChatMsg.meeting_voice_call);
    }
    else {
        console.log('无法获取有效的 sdkfileid，msgtype:', ChatMsg.msgtype);
        return "";
    }

    return sdkfileid
}

// 语音通话
// meetingVoiceUrl = ref('')
// meetingVoiceUrl_cache = ref('')


// 获取媒体消息的方法
const fetchmediaMessage = async (message) => {
    const sdkfileid = get_sdkfileid(message)

    // 检查缓存
    if (message.msgType === 'image' || message.msgType === 'emotion') {
        if (imageUrls_cache.value[sdkfileid]) {
            imageUrls.value[sdkfileid] = imageUrls_cache.value[sdkfileid];
            mediaLoadingCount.value++;
            checkAllMediaLoaded();
            return;
        }
    } else if (message.msgType === 'voice' || message.msgType === 'meeting_voice_call') {
        if (voiceUrls_cache.value[sdkfileid]) {
            voiceUrls.value[sdkfileid] = voiceUrls_cache.value[sdkfileid];
            mediaLoadingCount.value++;
            checkAllMediaLoaded();
            return;
        }
    } else if (message.msgType === 'video') {
        if (videoUrls_cache.value[sdkfileid]) {
            videoUrls.value[sdkfileid] = videoUrls_cache.value[sdkfileid];
            mediaLoadingCount.value++;
            checkAllMediaLoaded();
            return;
        }
    } else if (message.msgType === 'file') {
        if (fileUrls_cache.value[sdkfileid]) {
            fileUrls.value[sdkfileid] = fileUrls_cache.value[sdkfileid];
            mediaLoadingCount.value++;
            checkAllMediaLoaded();
            return;
        }
    }

    if (!message.chatMsg) {
        console.log('message.chatMsg 不存在，退出下载流程');
        return;
    }

    const ChatMsg = JSON.parse(message.chatMsg);
    if (!ChatMsg || !ChatMsg.msgtype) {
        console.log('ChatMsg 或 msgtype 不存在，退出下载流程');
        return;
    }

    console.log('开始下载媒体文件，msgtype:', ChatMsg.msgtype, 'sdkfileid:', sdkfileid);

    try {

        const jwt_token = localStorage.getItem('access_token');
        axiosInstance.post('/api/chatmessage/chatmedia/download', {
            sdkfileid: sdkfileid
        }, {
            headers: { Authorization: 'Bearer ' + jwt_token },
            responseType: 'arraybuffer'
        }).then(async res => {
            let blob = null;
            let url = null;
            if (ChatMsg.msgtype === 'image') {
                blob = new Blob([res.data], { type: 'image/jpeg' });
                url = URL.createObjectURL(blob);

                // 将 URL 存储到 imageUrls 中
                imageUrls.value[sdkfileid] = url;
                imageUrls_cache.value[sdkfileid] = url;

                console.log('获取到图片媒体')
                handleMessageContent(message)

                // console.log("附件下载接口调用成功：jpeg", url);
            } else if (ChatMsg.msgtype === 'emotion') {
                // 动态图可能是 gif 格式
                blob = new Blob([res.data], { type: 'image/gif' });
                url = URL.createObjectURL(blob);

                // 将 URL 存储到 imageUrls 中
                imageUrls.value[sdkfileid] = url;
                imageUrls_cache.value[sdkfileid] = url;

                console.log('获取到表情媒体')
                handleMessageContent(message)

            } else if (ChatMsg.msgtype === 'voice') {
                //需要将res.data转换为可播放的音频格式,用benz-amr-recorder将amr的arrybuffer数据流转换成wav链接
                try {

                    // 创建 BenzAMRRecorder 实例
                    const amrRecorder = new BenzAMRRecorder();

                    // 将 ArrayBuffer 转换为 Uint8Array
                    const uint8Array = new Uint8Array(res.data);
                    console.log('AMR 数据长度:', uint8Array.length);

                    console.log('res.data 类型:', Object.prototype.toString.call(res.data));
                    // 如果是 ArrayBuffer，查看前 10 字节的十六进制
                    const hexHeader = Array.from(uint8Array.slice(0, 10)).map(b => b.toString(16).padStart(2, '0')).join(' ');
                    console.log('文件头（Hex）:', hexHeader);

                    // 使用实例方法解码 AMR
                    const pcmData = await amrRecorder.decodeAMRAsync(uint8Array);
                    if (!pcmData) {
                        console.error('decodeAMRAsync 返回空数据');

                    }
                    console.log('解码后的 PCM 数据长度:', pcmData.length);

                    // 将 PCM 数据转换为 AudioBuffer 然后转换为 Blob
                    const audioContext = new AudioContext();
                    const audioBuffer = audioContext.createBuffer(1, pcmData.length, 8000); // AMR 采样率为 8000Hz
                    audioBuffer.getChannelData(0).set(pcmData);

                    // 将 AudioBuffer 转换为 WAV Blob
                    const audioBlob = audioBufferToWavBlob(audioBuffer);
                    const audioUrl = URL.createObjectURL(audioBlob);

                    voiceUrls.value[sdkfileid] = audioUrl;
                    voiceUrls_cache.value[sdkfileid] = audioUrl;

                    console.log('获取到语音媒体,audioUrl:', audioUrl)
                    console.log('语音媒体类型:', ChatMsg.msgtype)
                    handleMessageContent(message)

                    // 清理资源
                    audioContext.close();
                    amrRecorder.destroy();
                } catch (error) {
                    console.error('语音解码失败:', error);
                    // 可以在这里添加错误处理，比如显示错误消息
                }
            } else if (ChatMsg.msgtype === 'meeting_voice_call') {
                try {
                    console.log('开始处理会议语音(MP3格式)');

                    const uint8Array = new Uint8Array(res.data);
                    console.log('MP3数据长度:', uint8Array.length);

                    // 检查文件头
                    const hexHeader = Array.from(uint8Array.slice(0, 4)).map(b => b.toString(16).padStart(2, '0')).join(' ');
                    console.log('MP3文件头(Hex):', hexHeader);

                    // 直接创建MP3的Blob URL
                    const mp3Blob = new Blob([uint8Array], { type: 'audio/mpeg' });
                    const audioUrl = URL.createObjectURL(mp3Blob);

                    // 存储URL
                    voiceUrls.value[sdkfileid] = audioUrl;
                    voiceUrls_cache.value[sdkfileid] = audioUrl;

                    console.log('MP3语音URL创建成功:', audioUrl);
                    handleMessageContent(message);

                } catch (error) {
                    console.error('MP3语音处理失败:', error);
                    // 错误处理逻辑
                }
            }
            else if (ChatMsg.msgtype === 'video') {
                blob = new Blob([res.data], { type: 'video/mp4' });
                url = URL.createObjectURL(blob);
                videoUrls.value[sdkfileid] = url;
                videoUrls_cache.value[sdkfileid] = url;

                console.log('获取到视频媒体')
                handleMessageContent(message)
            } else if (ChatMsg.msgtype === 'file') {
                blob = new Blob([res.data], { type: 'application/octet-stream' });
                url = URL.createObjectURL(blob);
                fileUrls.value[sdkfileid] = url;
                fileUrls_cache.value[sdkfileid] = url;

                console.log('获取到文件媒体')
                handleMessageContent(message)
            }

            // 增加媒体加载计数
            // mediaLoadingCount.value++;
            // console.log('此处获取媒体消息')

        }).catch(error => {
            console.log(error);
            // 即使加载失败也要计数

            checkAllMediaLoaded();
            ElMessage.error('附件下载失败，请检查网络或联系管理员');
        });
    } catch (error) {
        console.error('处理媒体消息时出错:', error);
        // 出错时也要计数
        mediaLoadingCount.value++;
        checkAllMediaLoaded();
    } finally {
        console.log('获取媒体消息finally')
        checkAllMediaLoaded();
        mediaLoadingCount.value++;
    }
}

// 添加检查所有媒体是否加载完成的方法
const checkAllMediaLoaded = () => {
    // console.log('检查所有媒体消息是否加载完成')
    if (mediaLoadingCount.value === totalMediaCount.value && totalMediaCount.value > 0) {
        // 所有媒体加载完成后，执行滚动
        nextTick(() => {
            scrollToBottom();
        });
    }
}

const isProcessing = ref(false)
// 监听消息列表变化，处理撤回消息,图片、表情消息
watch(chatDetailMessages, async (newMessages) => {
    if (!newMessages) {
        isProcessing.value = true;
        return;
    }

    const promises = newMessages.map(async message => {
        if (message.msgType === 'revoke') {
            fetchRecallMessage(message);
        } else if (message.msgType === 'image' || message.msgType === 'emotion' || message.msgType === 'voice' || message.msgType === 'video' || message.msgType === 'file' || message.msgType === 'meeting_voice_call') {
            await fetchmediaMessage(message);
        } else {
            handleMessageContent(message);
        }

    });

    try {
        await Promise.all(promises);

    } catch (error) {
        console.log('处理消息失败', error);
    } finally {
        console.log('ttttttttttttttttttttttt')
        scrollToBottom()
        isProcessing.value = true;
    }
}, { immediate: true });




//调用高德地图API返回一张静态定位图
// const getMapImage = (message) => {
//     const key = 'a924f785e2522273c9b4113602e77dd0'
//     const image_width = 500
//     const image_height = 260
//     const longitude = message.longitude//经度
//     const latitude = message.latitude//纬度
//     const zoom = 15

//     const url = `https://restapi.amap.com/v3/staticmap?location=${longitude},${latitude}&zoom=${zoom}&size=${image_width}*${image_height}&markers=mid,,A:${longitude},${latitude}&key=${key}`

//     return url
// }










// 聊天记录弹窗相关
const chatRecordVisible = ref(false)
const currentChatRecord = ref(null)

// const showChatRecord = (message) => {
//     // 处理聊天记录数据，确保数据格式正确
//     const formattedRecords = message.item.map(item => {
//         // 根据不同的记录类型进行格式化
//         let formattedItem = {
//             timestamp: message.item.msgtime,  // 使用记录项的时间
//         }

//         // 根据不同的记录类型设置不同的消息类型和内容
//         switch (item.type) {
//             case 'ChatRecordText':
//                 formattedItem.msgtype = 'text'
//                 formattedItem.content = item.content
//                 break
//             case 'ChatRecordImage':
//                 formattedItem.msgtype = 'image'
//                 formattedItem.content = item.content
//                 break
//             case 'ChatRecordFile':
//                 formattedItem.msgtype = 'file'
//                 formattedItem.filename = item.content.filename
//                 formattedItem.filesize = item.content.filesize
//                 formattedItem.fileext = item.content.fileext
//                 formattedItem.fileurl = item.content.fileurl
//                 break
//             // 可以根据需要添加其他类型的处理
//         }

//         return formattedItem
//     })

//     currentChatRecord.value = {
//         title: message.title,
//         item: formattedRecords
//     }
//     chatRecordVisible.value = true
// }

// 对于会话记录消息类型，展示时将一些非文本类型消息用对应的类型信息进行标识
// const nonTextMessage_ToText = (item) => {
//     // 若传入的 item 为空，直接返回空字符串
//     if (!item) {
//         return '';
//     }
//     // 定义消息类型到文本描述的映射
//     const messageTypeMap = {
//         'ChatRecordText': item.content,
//         'ChatRecordImage': '[图片]',
//         'ChatRecordFile': '[文件]',
//         'ChatRecordVoice': '[语音]',
//         'ChatRecordVideo': '[视频]',
//         'ChatRecordLocation': '[位置]',
//         'ChatRecordCard': '[名片]',
//         'ChatRecordSharing': '[分享]',
//         'ChatRecordSystem': '[系统消息]'
//     };
//     // 根据 item 的 type 属性从映射中获取对应的文本描述
//     const text = messageTypeMap[item.type];
//     // 如果映射中存在对应的文本描述，则返回该描述；否则返回 '未知消息'
//     return text || '[未知消息]';
// };

//------------------------------------------------------------------------------------------动态调整会话详情面板的宽度

// 从父组件HomePage.vue中获取FunctionBar的开启状态
const FunctionBar_isCollapse = inject('FunctionBar_isCollapse')
// 获取当前展示的列表类型，如果是groupchat
const currentComponent_List = inject('currentComponent_List')
const chatBoardStyle = ref({
    width: 'calc(100vw - 40.5rem)' // 初始宽度 (660px -> 41.25rem)
})

// 更新聊天面板宽度的计算函数
const updateChatBoardWidth = (totalWidth) => {
    if (currentComponent_List.value == 'GroupList') {
        totalWidth = totalWidth - 19
    }
    chatBoardStyle.value.width = `calc(100vw - ${totalWidth}rem)`
    // console.log('当前计算的面板总宽度：', chatBoardStyle.value.width)
}

// 监听FunctionBar宽度变化
watch(() => FunctionBar_isCollapse.value, (newWidth) => {
    let totalWidth = 40.5
    if (!newWidth) {
        // console.log('展开状态：', newWidth, '当前currentComponent_List：', currentComponent_List.value)
        totalWidth = 46  // 750px -> 46.875rem
        updateChatBoardWidth(totalWidth)
    } else {
        // console.log('折叠状态：', newWidth, '当前currentComponent_List：', currentComponent_List.value)
        updateChatBoardWidth(totalWidth)

    }
}, { immediate: true })

// 监听FunctionBar宽度变化
watch(() => currentComponent_List.value, () => {
    let totalWidth = 40.5
    if (!FunctionBar_isCollapse.value) {
        // console.log('展开状态：', FunctionBar_isCollapse.value, '当前currentComponent_List：', currentComponent_List.value)
        totalWidth = 46  // 750px -> 46.875rem
        updateChatBoardWidth(totalWidth)
    } else {
        // console.log('折叠状态：', FunctionBar_isCollapse.value, '当前currentComponent_List：', currentComponent_List.value)
        updateChatBoardWidth(totalWidth)

    }
}, { immediate: true })





// 创建聊天内容区域的引用
const chatContentRef = ref(null)
// 控制加载状态，防止重复加载
const isLoading = ref(false)
// 标识是否还有更多消息可以加载
const hasMore = ref(true)
// 当前页码，用于分页请求
const currentPage = ref(1)
// 距离顶部触发加载的阈值（像素），当滚动到距离顶部100px时触发加载
const loadMoreThreshold = 100
// 记录上一次的滚动位置
const lastScrollTop = ref(0)

// 滚动处理函数
const handleScroll = async (e) => {
    const { scrollTop } = e.target
    // 只有向上滚动且接近顶部时才触发加载更多
    if (scrollTop < lastScrollTop.value && chatDetailMessagesTotal.value < selectedChat.value.limit) {
        if (hasMore.value) {
            ElMessage.warning('已加载全部消息');
            hasMore.value = false
        }
    }

    if (scrollTop < loadMoreThreshold && !isLoading.value && hasMore.value) {
        await loadMoreMessages()
    }
    // 更新上一次的滚动位置
    lastScrollTop.value = scrollTop
}

// 加载更多消息
const loadMoreMessages = async () => {
    if (isLoading.value) {
        return
    }

    isLoading.value = true
    try {
        currentPage.value += 1
        // console.log('获取页码：', currentPage.value)

        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, searchName.value, currentPage.value, selectedChat.value.limit)

    } catch (error) {
        console.error('加载消息失败:', error)
    } finally {
        isLoading.value = false
    }
}

// 滚动到底部
const scrollToBottom = async () => {
    //当前页数不是1的情况下不滚动到底部
    console.log('滚动到底部')
    if (currentPage.value !== 1) {
        return
    }
    //确保在DOM完全更新后再执行滚动，VUE的响应式更新是异步的，消息列表更新之后，DOM不会立即渲染，netTIck会等待VU完成DOM更新后才执行回调，这样就保证了scrollHeight的值是最新的，包含了所有消息的高度
    await nextTick()
    const chatContent = chatContentRef.value
    if (chatContent) {
        //scrollTop为元素已经滚动的高度
        chatContent.scrollTop = chatContent.scrollHeight
    }
}



// 在 script setup 部分添加高亮文本的方法
const highlightText = (text, keyword) => {

    if (!keyword || !text) return text;
    try {
        // 转义特殊字符
        const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(escapedKeyword, 'gi');
        return text.replace(regex, match => `<span class="highlight">${match}</span>`);
    } catch (error) {
        console.error('高亮处理出错:', error);
        return text;
    }
}

// 添加图片预览相关的状态变量
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

// 添加基础尺寸相关的状态变量
const scale = ref(1)
const baseWidth = ref(0)
const baseHeight = ref(0)
const minScale = 0.1  // 降低最小缩放比例
const maxScale = 5    // 提高最大缩放比例
const scaleStep = 0.1

// 处理图片加载完成事件
const handleImageLoad = (e) => {
    const img = e.target
    baseWidth.value = img.naturalWidth
    baseHeight.value = img.naturalHeight

    // 计算初始缩放比例，使图片适应屏幕
    const maxWidth = window.innerWidth * 0.9
    const maxHeight = window.innerHeight * 0.9

    // 计算宽高比
    const imageRatio = baseWidth.value / baseHeight.value
    const screenRatio = maxWidth / maxHeight

    let initialScale
    if (imageRatio > screenRatio) {
        // 图片更宽，以宽度为基准
        initialScale = maxWidth / baseWidth.value
    } else {
        // 图片更高，以高度为基准
        initialScale = maxHeight / baseHeight.value
    }

    // 确保初始缩放比例在合理范围内
    scale.value = Math.min(Math.max(initialScale, minScale), maxScale)
}

// 处理鼠标滚轮事件
const handleWheel = (e) => {
    // 计算新的缩放比例
    const delta = e.deltaY > 0 ? -scaleStep : scaleStep
    const newScale = scale.value + delta

    // 限制缩放范围
    if (newScale >= minScale && newScale <= maxScale) {
        scale.value = newScale
    }
}

// 修改图片点击处理函数，重置缩放比例
const handleImageClick = (message) => {
    const imageUrl = getImageUrl(message)
    if (imageUrl) {
        previewImageUrl.value = imageUrl
        imagePreviewVisible.value = true
        scale.value = 1 // 重置缩放比例
        document.body.style.overflow = 'hidden'
    }
}

// 修改关闭图片预览函数，重置缩放比例
const closeImagePreview = () => {
    imagePreviewVisible.value = false
    scale.value = 1 // 重置缩放比例
    document.body.style.overflow = 'auto'
}






</script>

<style>
/* 整个会话详情面板 */
.chat-board {
    height: calc(100vh - 5rem);
    /* width 由 JavaScript 动态计算 */
    display: flex;
    flex-direction: column;
    background-color: #f7f7f7;
    border-left: 0.0625rem solid #e6e6e6;
    transition: 0.4s;
}

.chat-board-nonShow {
    height: calc(100vh - 5rem);
    /* width 由 JavaScript 动态计算 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    background-color: #f7f7f7;
    border-left: 0.0625rem solid #e6e6e6;
    transition: 0.4s;
}

.chat-board-nonShow img {
    width: 30rem;
    height: 30rem;
}

.chat-board-nonShow h3 {
    font-size: 1.4rem;
    color: #5478eb;
}

/* 顶部操作区域 */
.operate-area {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 2.0625rem;
    padding: 0.125rem 0.8125rem;
    background-color: #ffffff;
}


/* 会话详情面中顶部的收发信人的信息 */
.operate-area .detailChat-info {
    margin-right: 0.625rem;
}

.operate-area .detailChat-info .sender,
.operate-area .detailChat-info .receiver {
    color: #2c2c2c;
    font-size: 1.04rem;
    font-weight: 500;
}


/* 会话详情面中顶部的刷新按钮 */
.operate-area .refresh-area {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}


/* 搜索区域 */
.search-area {
    padding: 0 1rem;
    padding-top: 0.3125rem;
    padding-bottom: 0.8125rem;
    background-color: #ffffff;
    border-bottom: 0.0625rem solid #e6e6e6;
    display: flex;
    flex-direction: row;
    align-items: center;
}

/* 搜索输入框 */
.search-input {
    width: 25%;
    display: flex;
    flex-direction: row;
    align-items: center;
}

/* 搜索图标 */
.search-icon {
    color: #909399;
}

/* 筛选条件按钮 */
.filter-options {
    margin-left: 0.625rem;
}

/* 筛选条件按钮组：消息、文件、图片 */
.filter-type {
    margin-left: 0.625rem;
    border: 0.0625rem solid #add9f5;
}

/* 筛选条件按钮 */
.filter-type .el-button {
    margin-left: 0;
    border-radius: 0;
    border: none;
}

/* 整体聊天内容区域 */
.chat-content {
    flex: 1;
    overflow-y: auto;
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
    position: relative;
}

.chat-content::-webkit-scrollbar {
    width: 0.375rem;
}

.chat-content::-webkit-scrollbar-thumb {
    background-color: #e0e0e0;
    border-radius: 0.1875rem;
}

.chat-content::-webkit-scrollbar-track {
    background-color: transparent;
}


/* 控制单条消息的默认位置，靠左*/
.message-container {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-top: 1.875rem;
    width: 100%;
}

/* 控制发送方单条消息位置，靠右 */
.message-container.sent {
    flex-direction: row-reverse;
}

/* 单条消息的头像默认样式 */
.avatar {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    /* background: linear-gradient(135deg, #1890ff, #0960bd); */
    /* color: white; */
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(22, 119, 255, 0.2);

}

/* 发送方单条消息的div样式 */
.sent .avatar {
    background: linear-gradient(135deg, #ffffff, #f0f0f0);
    border-radius: 0.5rem;
    flex-shrink: 0;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0.5rem;
}



/* 单条消息的气泡内容、发送方的名字、标签、时间 */
.message-content {
    max-width: 60%;
}

/* 发送方名字、标签、时间样式 */
.sent .sender-info {
    text-align: right;
    margin-bottom: 5px;
}


/* 单条消息的时间内容默认样式 */
.sender-info .message-time {
    text-align: center;
    margin: 16px 0;
    color: #909399;
    font-size: 12px;
}

/* 单条消息的发送/接收方的标签默认样式 */
.sender-info .sender-lable {
    font-size: 13px;

    margin-left: 5px;
    margin-right: 5px;
}

.sender-info .sender-lable.employee {
    color: #1890ff;
}

.sender-info .sender-lable.wechat {
    color: #13d153;
}

.sender-info .sender-lable.other {
    color: #ff8432;
}

/* 单条消息的发送/接收方的名称默认样式 */
.sender-info .sender-name {
    font-size: 12px;
    color: #606266;
    margin-bottom: 4px;
}

/* 单条消息的气泡默认样式 */
.message-bubble {
    padding: 12px 16px;
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    font-size: 14px;
    line-height: 1.5;
    word-break: break-word;

    margin-top: 6px;
}

/* 发送方单条消息的气泡颜色样式 */
.sent .message-bubble {
    background-color: #eef8ff;
}

/* 图片类型消息 */
.message-bubble img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}





/* 卡片样式 */
.message-bubble .card-content {
    /* 给卡片内容设置基础样式，可根据整体布局调整 */
    display: flex;
    flex-direction: column;
    justify-content: center;

    max-width: 500px;
    /* 限制卡片最大宽度，可按需改 */
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding: 15px;
}

.top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.left {
    display: flex;
    flex-direction: column;
}

.card-company {
    font-size: 16px;
    color: #333;
    margin-bottom: 15px;
}

.card-userId {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
}

.card-Name {
    font-size: 14px;
    color: #999;
}

.right {
    display: flex;
    align-items: center;
}

.card-avatar img {
    /* 添加背景阴影 */
    box-shadow: 0 1px 2px rgba(94, 94, 94, 0.3);
    width: 40px;
    height: 40px;
    margin-left: 20px;
}

.bottom {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
    padding-top: 10px;
    border-top: solid 1px rgba(150, 150, 150, 0.1);
}

/* 链接样式 */
.message-bubble.link-content {
    padding: 0;
    margin-bottom: 16px;
    transition: all 0.3s ease;
}

.link-card-container {
    display: flex;

    background-color: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.link-card-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.link-image-container {
    /* background-color: #f5f7fa; */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 120px;
    /* 保持原始宽度 */
    height: 80px;
    /* 保持原始高度 */
    overflow: hidden;
    /* 防止图片溢出容器 */

    padding: 13px;

}

.link-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    /* 保持图片宽高比，完整显示在容器内 */
    width: auto;
    /* 让宽度自适应高度 */
    height: auto;
    /* 让高度自适应宽度 */
}

.link-content {
    flex: 1;
    padding: 12px 0px;
    padding-right: 18px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.link-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 8px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.title-with-symbol {
    padding-left: 24px;
    position: relative;
}

.title-with-symbol::before {
    content: attr(data-symbol);
    position: absolute;
    left: 0;
    top: 0;
    background-color: #ff4d4f;
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
}

.link-description {
    font-size: 14px;
    color: #4d4d4d;
    margin-bottom: 8px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.link-url {
    font-size: 12px;
    color: #0070f3;
    display: flex;
    align-items: center;
}

.url-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
}

.icon-external-link {
    margin-left: 4px;
    font-size: 12px;
}




/* 地图样式 */
.message-bubble.map-content {
    box-sizing: border-box;
    padding: 0;

    border: 1px solid #e6e6e6;
}


.message-bubble .location-address {
    display: flex;
    align-items: center;
    justify-content: center;

    background-color: #f1f1f1;
    padding: 3px 0;
}

/* 链接消息 */






/* 小程序消息 */
.message-bubble.weapp-content {
    width: 100%;
    padding: 10px;
}

.weapp-content .top .weapp-info {
    display: flex;
    flex-direction: row;
    align-items: center;

    margin-bottom: 5px;
}

.weapp-content .top .weapp-info .weapp-default-icon {
    width: 24px;
    height: 24px;
}

.weapp-content .top .weapp-info .weapp-title {
    margin-left: 5px;
}

.weapp-content .top .weapp-default-image {
    background-color: #eeeeee;

    width: 100%;
    height: 200px;

    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    margin-top: 5px;
}

.weapp-content .bottom {
    display: flex;
    flex-direction: row;
    align-items: center;

    margin-top: 8px;

    border-top: 1px solid #e6e6e6;
}

.weapp-content .bottom .weapp-mini-icon {
    width: 12px;
    height: 12px;
}

.weapp-content .bottom span {
    font-size: 12px;
    color: #909399;
    margin-left: 3px;
    margin-top: 2px;
}

/* 会话记录消息 */
.message-bubble.chatrecord-content {
    cursor: pointer;
}

.chatrecord-content .chatrecord-part {
    padding: 0 3px;
    margin-top: 4px;
    color: #909399;
    font-weight: 300;
}

/* 待办消息样式 */
.message-bubble.todo-content {
    width: 300px;
    padding: 0;
    border: 1px solid #e6e6e6;
    /* box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); */
    transition: all 0.3s ease;
}

.todo-content .todo-title {
    padding: 12px 16px;
    background-color: #f0f7ff;
    color: #1677ff;
    font-size: 15px;
    font-weight: 500;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    align-items: center;
}

.todo-content .todo-title::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background-color: #1677ff;
    margin-right: 12px;
    border-radius: 2px;
}

.todo-content .todo-content {
    padding: 12px 16px;
    color: #666;
    font-size: 14px;
    line-height: 1.6;
    background-color: #ffffff;
}

/* 发送方待办消息样式覆盖 */
.sent .message-bubble.todo-content {
    background-color: #ffffff;
}

.sent .todo-content .todo-title {
    background-color: #e6f4ff;
}

/* 投票消息样式 */
.message-bubble.vote-content {
    width: 300px;
    padding: 0;
    border: 1px solid #e6e6e6;
    background-color: #ffffff;
    border-radius: 8px;
}

/* 发起投票样式 */
.vote-initiate {
    display: flex;
    flex-direction: column;
}

.vote-header {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vote-title {
    font-size: 15px;
    font-weight: 500;
    color: #333;
}

.vote-options {
    padding: 12px 16px;
}

.vote-option {
    margin-bottom: 12px;
}

.vote-option:last-child {
    margin-bottom: 0;
}

/* 参与投票样式 */
.vote-participate {
    display: flex;
    flex-direction: column;
}

.vote-selected {
    padding: 12px 16px;
}

.selected-option {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: #67c23a;
}

.selected-option .el-icon {
    margin-right: 8px;
}

/* 投票底部样式 */
.vote-footer {
    padding: 8px 16px;
    border-top: 1px solid #f0f0f0;
    background-color: #fafafa;
}

.vote-id {
    font-size: 12px;
    color: #999;
}

/* 发送方投票消息样式覆盖 */
.sent .vote-content {
    background-color: #ffffff;
}

.sent .vote-header {
    background-color: #f0f7ff;
}

.sent .vote-footer {
    background-color: #f0f7ff;
}

/* 填表消息样式 */
.message-bubble.collect-content {
    width: 350px;
    padding: 0;
    border: 1px solid #e6e6e6;
    background-color: #ffffff;
    border-radius: 8px;
}

/* 表单头部 */
.collect-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f9f9f9;
}

.collect-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.collect-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.collect-room {
    font-size: 12px;
    color: #666;
}

/* 表单主体 */
.collect-body {
    padding: 16px;
}

.collect-item {
    margin-bottom: 16px;
}

.collect-item:last-child {
    margin-bottom: 0;
}

.item-label {
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
}

.item-required {
    color: #f56c6c;
    margin-right: 4px;
}

.item-input {
    width: 100%;
}

.item-input :deep(.el-input),
.item-input :deep(.el-input-number),
.item-input :deep(.el-date-picker),
.item-input :deep(.el-time-picker) {
    width: 100%;
}

/* 表单底部 */
.collect-footer {
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
    background-color: #f9f9f9;
}

/* 发送方填表消息样式覆盖 */
.sent .collect-content {
    background-color: #ffffff;
}

.sent .collect-header,
.sent .collect-footer {
    background-color: #f0f7ff;
}


/* TODO：红包消息样式  */
.message-bubble.redpacket-content {
    padding: 0;
    width: 240px;
    background-color: #f43f3b;
    border-radius: 8px;
    overflow: hidden;
}

.redpacket-wrapper {
    display: flex;
    flex-direction: column;
    color: #fff;
    cursor: pointer;
}

.redpacket-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0;
}

.redpacket-icon i {
    font-size: 36px;
}

.redpacket-info {
    padding: 0 16px 16px;
    text-align: center;
}

.redpacket-wish {
    font-size: 14px;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.redpacket-amount {
    font-size: 24px;
    font-weight: bold;
}

.redpacket-type {
    background-color: rgba(0, 0, 0, 0.1);
    padding: 8px 0;
    text-align: center;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.9);
}

/* 发送方红包样式 */
.sent .redpacket-content {
    background-color: #ff6b6b;
}

.redpacket-wrapper:hover {
    opacity: 0.95;
}

/* 会议消息样式 */
.message-bubble.meeting-content {
    width: 320px;
    padding: 0;
    background-color: #ffffff;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
}

.meeting-header {
    padding: 12px 16px;
    background-color: #f0f7ff;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    align-items: center;
}

.meeting-header i {
    color: #1677ff;
    font-size: 18px;
    margin-right: 8px;
}

.meeting-title {
    font-size: 15px;
    font-weight: 500;
    color: #1677ff;
    flex: 1;
}

.meeting-type {
    font-size: 12px;
    color: #1677ff;
    background-color: rgba(22, 119, 255, 0.1);
    padding: 2px 8px;
    border-radius: 4px;
}

.meeting-body {
    padding: 12px 16px;
}

.meeting-time,
.meeting-address,
.meeting-remarks {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    color: #606266;
    font-size: 14px;
}

.meeting-time:last-child,
.meeting-address:last-child,
.meeting-remarks:last-child {
    margin-bottom: 0;
}

.meeting-time i,
.meeting-address i,
.meeting-remarks i {
    font-size: 14px;
    margin-right: 8px;
    margin-top: 3px;
    color: #909399;
}

.time-details {
    flex: 1;
}

.time-row {
    display: flex;
    margin-bottom: 4px;
}

.time-row:last-child {
    margin-bottom: 0;
}

.time-label {
    color: #909399;
    margin-right: 4px;
    min-width: 70px;
}

.remarks-label {
    color: #909399;
    margin-right: 4px;
}

.remarks-content {
    flex: 1;
    word-break: break-all;
}

.meeting-footer {
    padding: 8px 16px;
    background-color: #f7f7f7;
    border-top: 1px solid #e6e6e6;
}

.meeting-id {
    font-size: 12px;
    color: #909399;
}

/* 发送方会议消息样式覆盖 */
.sent .meeting-content {
    background-color: #ffffff;
}

.sent .meeting-header {
    background-color: #f0f7ff;
}

.sent .meeting-footer {
    background-color: #f7f7f7;
}

/* 切换企业日志消息样式 */
.message-container .switch-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 10px;
    width: 100%;
}

.switch-label .switch-time {
    font-size: 12px;
    color: #909399;
    margin-bottom: 7px;
    text-align: center;
    margin-top: 30px;
}

.switch-label p {
    font-size: 13px;
    color: #606266;
    text-align: center;
    background-color: #f2f6fc;
    width: 150px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    margin: 0 auto;
}

/* 在线文档消息样式 */
.docmsg-content {
    width: 280px;
    padding: 0;
    background-color: #ffffff;
    border: 1px solid #e6e6e6;
    cursor: pointer;
}

.docmsg-content .top {
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    align-items: center;
}

.docmsg-content .top .docmsg-icon-mini {
    width: 20px;
    height: 20px;
    margin-right: 8px;
}

.docmsg-content .top .docmsg-type {
    font-size: 13px;
    color: #606266;
}

.docmsg-content .bottom {
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.docmsg-content .bottom .docmsg-msg {
    flex: 1;
    margin-right: 12px;
}

.docmsg-content .bottom .docmsg-title {
    display: block;
    font-size: 14px;
    color: #303133;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.docmsg-content .bottom .docmsg-creator {
    display: block;
    font-size: 12px;
    color: #909399;
}

.docmsg-content .bottom .docmsg-icon {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
}

/* 发送方在线文档消息样式覆盖 */
.sent .docmsg-content .top {
    background-color: #f0f7ff;
}

.docmsg-content:hover {
    background-color: #f5f7fa;
}

/* 日程消息样式 */
.calendar-content {
    width: 300px;
    padding: 0;
    background-color: #ffffff;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    overflow: hidden;
}

.calendar-header {
    padding: 12px 16px;
    background-color: #f0f7ff;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    align-items: center;
    gap: 8px;
}

.calendar-header img {
    width: 20px;
    height: 20px;
    object-fit: contain;
}

.calendar-type {
    font-size: 14px;
    color: #1677ff;
    font-weight: 500;
}

.calendar-body {
    padding: 16px;
}

.calendar-title {
    font-size: 15px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 16px;
}

.calendar-info .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    color: #606266;
    font-size: 13px;
    line-height: 1.5;
}

.calendar-info .info-item:last-child {
    margin-bottom: 0;
}

.calendar-info .info-item .el-icon {
    margin-right: 8px;
    margin-top: 3px;
    color: #909399;
    flex-shrink: 0;
    font-size: 16px;
}

.calendar-info .info-item span {
    flex: 1;
    word-break: break-all;
}

.calendar-info .info-item span+span {
    margin-left: 8px;
}

/* 发送方日程消息样式覆盖 */
.sent .calendar-content {
    background-color: #ffffff;
}

.sent .calendar-header {
    background-color: #f0f7ff;
}

.calendar-info .info-item.time-info {
    align-items: flex-start;
}

.calendar-info .time-details {
    flex: 1;
}

.calendar-info .time-details .time-row {
    color: #606266;
    margin-bottom: 4px;
}

.calendar-info .time-details .time-row:last-child {
    margin-bottom: 0;
}



/* 加载更多提示样式 */
.loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    color: #909399;
    font-size: 0.875rem;
}

.loading-more .loading-icon {
    margin-right: 0.5rem;
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}



/* ----------------------------------------------------------------------------- */
/* 文本消息内容样式 */
.text-content {
    max-width: 100%;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.text-content p {
    margin: 0;
    padding: 0;
    line-height: 1.6;
    color: #333;
}


/* 发送方文本消息样式 */
.sent .text-content {
    background-color: #eef8ff;
}

.sent .text-content p {
    color: #333;
}

/* ----------------------------------------------------------------------------- */
/* 图片消息样式 */
.message-bubble.image-content {
    max-width: 300px;
    /* max-height: 400px; */
    padding: 0;
    overflow: hidden;
    border-radius: 4px;
    background-color: transparent;
}

.message-bubble.image-content img {
    width: 100%;
    height: 100%;
    /*保持图片比例，确保完整显示 */
    object-fit: contain;
    display: block;
}

/* 发送方图片消息样式 */
.sent .message-bubble.image-content {
    background-color: transparent;
}

/* ----------------------------------------------------------------------------- */
/* 文件消息样式  */
.message-bubble.file-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    max-width: 90%;
    background-color: #ffffff;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.file-content:hover {
    background-color: #f5f7fa;
}

.file-content .left {
    flex: 1;
    overflow: hidden;
    margin-right: 5px;
    min-width: 0;
    /* 确保flex子元素可以正确收缩 */
}

.file-content .left .file-name {
    font-size: 14px;
    color: #303133;
    margin: 0 0 4px 0;
    word-break: break-all;
    white-space: normal;
    overflow-wrap: break-word;
    line-height: 1.4;
}

.file-content .left .file-size {
    font-size: 12px;
    color: #909399;
    margin: 0;
}

.file-content .right {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-left: 8px;
}

.file-content .right img {
    width: 32px;
    height: 32px;
    object-fit: contain;
}

/* ----------------------------------------------------------------------------- */
/* 撤回消息样式  */
.message-container .recall-lable {
    display: flex;
    flex-direction: row;

    margin: auto;
    margin-left: 2px;

    background-color: #d1d1d1;
    border-radius: 4px;
    padding: 3px 4px;

    color: #fdfdfd;
    font-size: 12px;
    font-weight: 200;
    letter-spacing: 0.5px;

    cursor: default;

}

.message-container .recall-lable .el-icon {
    margin-right: 3px;
}

.message-container.sent .recall-lable {
    display: flex;
    flex-direction: row;

    margin: auto;
    margin-right: 2px;

    background-color: #d1d1d1;
    border-radius: 4px;
    padding: 3px 4px;

    color: #fdfdfd;
    font-weight: 200;
    letter-spacing: 0.5px;

    cursor: default;

}

/* ----------------------------------------------------------------------------- */
/* agree 类型消息*/
.message-container .agree,
.message-container .disagree {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;

    padding-top: 10px;
}

.agree .agree-time,
.disagree .disagree-time {
    font-size: 12px;
    color: #909399;
    margin-bottom: 7px;
    text-align: center;
}

.agree p,
.disagree p {
    font-size: 14px;
    color: #359632;
    text-align: center;
    background-color: #adff94;

    width: 150px;
    height: 25px;

    display: flex;
    align-items: center;
    justify-content: center;

    border-radius: 4px;

}

.disagree p {
    background-color: #ff9191;
    color: #cc3a3a;
}

/* ----------------------------------------------------------------------------- */
/* 语音消息样式 */
.voice-content {
    min-width: 200px;
    max-width: 350px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    border-radius: 12px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #e4e7ed;
}

.voice-content:hover {
    background-color: #f5f7fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.voice-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.voice-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.voice-play-btn {
    width: 36px;
    height: 36px;
    border: none;
    background-color: #5188ff;
    color: white;
    transition: all 0.3s ease;
}

.voice-play-btn:hover {
    background-color: #4070ff;
    transform: scale(1.05);
}

.voice-play-btn:active {
    transform: scale(0.95);
}

.voice-duration {
    color: #606266;
    font-size: 14px;
    font-weight: 500;
    margin-left: auto;
}

.voice-visual-container {
    width: 100%;
    height: 40px;
    position: relative;
    background-color: #f8f9fa;
    border-radius: 6px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.voice-waveform {
    width: 100%;
    height: 100%;
}

.voice-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #f0f0f0;
    width: 100%;
    height: 100%;
    color: #999;
    font-size: 12px;
}

.voice-loading .loading-icon {
    animation: rotate 1s linear infinite;
}

.voice-error {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #fef0f0;
    width: 100%;
    height: 100%;
    color: #f56c6c;
    font-size: 12px;
    border: 1px solid #fbc4c4;
    border-radius: 4px;
}

.voice-error {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #fef0f0;
    width: 100%;
    height: 100%;
    color: #f56c6c;
    font-size: 12px;
    border: 1px solid #fbc4c4;
    border-radius: 4px;
}

.voice-waveform-placeholder {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    height: 100%;
    padding: 0 8px;
}

.voice-bars {
    display: flex;
    align-items: center;
    gap: 2px;
    flex: 1;
}

.voice-bars .bar {
    width: 3px;
    background-color: #ddd;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.voice-bars .bar:nth-child(odd) {
    height: 20px;
}

.voice-bars .bar:nth-child(even) {
    height: 15px;
}

.voice-bars .bar:nth-child(3n) {
    height: 25px;
}

.voice-content.playing .voice-bars .bar {
    background-color: #5188ff;
    animation: wave 1.5s infinite ease-in-out;
}

.sent .voice-content.playing .voice-bars .bar {
    background-color: #1976d2;
}

.voice-text {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

@keyframes wave {

    0%,
    100% {
        transform: scaleY(1);
    }

    50% {
        transform: scaleY(1.5);
    }
}

/* 发送方语音消息样式覆盖 */
.sent .voice-content {
    background-color: #e3f2fd;
    border-color: #bbdefb;
}

.sent .voice-content:hover {
    background-color: #d6eafb;
}

.sent .voice-play-btn {
    background-color: #1976d2;
}

.sent .voice-play-btn:hover {
    background-color: #1565c0;
}

.sent .voice-visual-container {
    background-color: #e8f4fd;
}

/* 音频播放时的动画效果 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.voice-content.playing .voice-play-btn {
    animation: pulse 1.5s infinite;
}

/* vue-audio-visual 样式覆盖 */
.voice-waveform canvas {
    border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .voice-content {
        min-width: 180px;
        max-width: 280px;
        padding: 10px 12px;
    }

    .voice-play-btn {
        width: 32px;
        height: 32px;
    }

    .voice-visual-container {
        height: 35px;
    }
}

/* ----------------------------------------------------------------------------- */
/* 语音通话消息样式 */
.meeting-voice-content {
    min-width: 250px;
    max-width: 400px;
    padding: 16px;
    transition: all 0.3s ease;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
    box-shadow: 0 3px 12px rgba(81, 136, 255, 0.15);
    border: 1px solid #d4e6ff;
    position: relative;
}

.meeting-voice-content:hover {
    background: linear-gradient(135deg, #f0f4ff 0%, #dde8ff 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(81, 136, 255, 0.25);
}

.meeting-voice-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e0ebff;
}

.meeting-voice-icon {
    color: #5188ff;
    font-size: 18px;
    animation: pulse-icon 2s infinite;
}

.meeting-voice-title {
    color: #5188ff;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.meeting-voice-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
    cursor: pointer;
}

/* 发送方语音通话消息样式覆盖 */
.sent .meeting-voice-content {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #90caf9;
}

.sent .meeting-voice-content:hover {
    background: linear-gradient(135deg, #d6eafb 0%, #a5d6f7 100%);
}

.sent .meeting-voice-icon {
    color: #1976d2;
}

.sent .meeting-voice-title {
    color: #1976d2;
}

.sent .meeting-voice-header {
    border-bottom-color: #c5e1fd;
}

/* 语音通话图标动画 */
@keyframes pulse-icon {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .meeting-voice-content {
        min-width: 200px;
        max-width: 300px;
        padding: 12px;
    }

    .meeting-voice-icon {
        font-size: 16px;
    }

    .meeting-voice-title {
        font-size: 13px;
    }
}

/* ----------------------------------------------------------------------------- */
/* 视频消息样式 */
.video-wrapper {
    position: relative;
    max-width: 300px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background-color: #2a2a2a;
}

.video-container {
    position: relative;
    width: 100%;
}

.video-player {
    width: 100%;
    max-height: 200px;
    object-fit: contain;
    display: block;
    background-color: #000;
    border-radius: 8px;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
}

.video-overlay:hover {
    opacity: 1;
}

.play-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
}

.play-button .el-icon {
    font-size: 24px;
    color: #409eff;
}

.video-info {
    padding: 8px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #3a3a3a;
}

.video-duration {
    font-size: 12px;
    color: #fff;
    padding: 2px 5px;
    border-radius: 3px;
    background-color: rgba(0, 0, 0, 0.3);
}

.chat-msg-sent .video-wrapper {
    background-color: #2b82ff;
}

.chat-msg-sent .video-info {
    background-color: #1a70e8;
}

.chat-msg-received .video-wrapper {
    background-color: #383838;
}

.chat-msg-received .video-info {
    background-color: #2a2a2a;
}

/* 遮罩层样式 */
.content-overlay {
    position: absolute;
    top: auto;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    height: 100%;
    /* width: 100vw; */
}

.content-overlay .loading-icon {
    color: #409EFF;
    animation: rotate 1s linear infinite;
}

.content-overlay .loading-text {
    margin-top: 10px;
    color: #606266;
    font-size: 14px;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* 确保chat-content是relative定位，这样遮罩层的绝对定位才能正确覆盖 */
.chat-content {
    position: relative;
    /* ... 保持原有样式 ... */
}

/* 添加高亮样式 */
.highlight {
    background-color: #fff131;
    padding: 2px 4px;
    border-radius: 2px;
    color: #000;
    /*font-weight: bold;*/
}

/* 添加图片预览相关样式 */
.image-preview-dialog {
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-preview-dialog :deep(.el-dialog__body) {
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.9);
}

.preview-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
}

/* 修改图片消息样式，添加鼠标手型 */
.message-bubble.image-content {
    cursor: pointer;
}

/* 图片预览相关样式 */
.image-preview-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.85);
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-preview-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: visible;
    /* 允许内容溢出 */
}

.preview-image {
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
    transition: width 0.1s ease-out, height 0.1s ease-out;
    /* 添加平滑过渡效果 */
}

/* 图片消息样式 */
.message-bubble.image-content {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.message-bubble.image-content:hover {
    transform: scale(1.02);
}

/* 修改图片预览相关样式 */
.image-preview-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: visible;
    min-width: 100px;
    min-height: 100px;
}

.preview-image {
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
    transition: width 0.1s ease-out, height 0.1s ease-out;
    will-change: width, height;
    /* 优化性能 */
}

/* 文件操作弹窗样式 */
.file-dialog-content {
    padding: 20px;
}

.file-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 8px;
}

.file-icon {
    width: 40px;
    height: 40px;
    margin-right: 15px;
}

.file-details {
    flex: 1;
}

.file-details .file-name {
    font-size: 16px;
    color: #303133;
    margin: 0 0 5px 0;
    word-break: break-all;
}

.file-details .file-size {
    font-size: 14px;
    color: #909399;
    margin: 0;
}

.file-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.file-actions .el-button {
    min-width: 100px;
}

.preview-dialog {
    :deep(.el-dialog__body) {
        padding: 0;
        height: calc(80vh - 120px);
        overflow: hidden;
    }
}

.preview-container {
    height: 100%;
    overflow: auto;
    background-color: #f5f7fa;
    padding: 20px;
}

:deep(.vue-office-docx),
:deep(.vue-office-excel),
:deep(.vue-office-pdf) {
    height: 100%;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 快速筛选按钮样式 */
.filter-type {
    display: flex;
    gap: 8px;
    align-items: center;
}

.filter-type .el-button {
    transition: all 0.3s ease;
    border-radius: 6px;
    font-weight: 500;
}

.filter-type .el-button:not(.is-plain) {
    background: linear-gradient(135deg, #5188ff 0%, #4070ff 100%);
    border-color: #5188ff;
    box-shadow: 0 2px 8px rgba(81, 136, 255, 0.3);
    transform: translateY(-1px);
}

.filter-type .el-button:not(.is-plain):hover {
    background: linear-gradient(135deg, #4070ff 0%, #3060ff 100%);
    box-shadow: 0 4px 12px rgba(81, 136, 255, 0.4);
    transform: translateY(-2px);
}

.filter-type .el-button.is-plain {
    background-color: #ffffff;
    border-color: #dcdfe6;
    color: #606266;
}

.filter-type .el-button.is-plain:hover {
    background-color: #f5f7fa;
    border-color: #c0c4cc;
    color: #409eff;
}
</style>
