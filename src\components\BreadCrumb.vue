<template>
    <div class="breadcrumb-container">
        <el-breadcrumb :separator-icon="ArrowRight">
            <el-breadcrumb-item v-for="(item, index) in breadcrumbItems" :key="index">
                {{ item }}
            </el-breadcrumb-item>
        </el-breadcrumb>
    </div>
</template>

<script setup>
import { ArrowRight } from '@element-plus/icons-vue'
import { inject } from 'vue'

const breadcrumbItems = inject('breadcrumbItems')

</script>

<style>
.breadcrumb-container {
    margin-bottom: 5px;
}
</style>