{"version": 3, "file": "js/752.efe0089d.js", "mappings": "+zUAmDA,MAAMA,GAAaC,EAAAA,EAAAA,IAAI,IACjBC,GAAaD,EAAAA,EAAAA,IAAI,SAEjBE,GAAeF,EAAAA,EAAAA,IAAI,IACnBG,GAAiBH,EAAAA,EAAAA,IAAI,IAErBI,GAAcJ,EAAAA,EAAAA,IAAI,GAClBK,GAAWL,EAAAA,EAAAA,IAAI,KAEfM,GAAwBC,EAAAA,EAAAA,IAAO,yBAC/BC,GAA4BD,EAAAA,EAAAA,IAAO,6BAGnCE,EAAkBA,CAACC,EAASC,EAAYC,KAE1CN,EAAsBO,MAAQ,eAC9BL,EAA0BK,MAAQ,mBAGlC,MAAMC,EAAYC,aAAaC,QAAQ,gBAEvCC,QAAQC,IAAI,cAEZC,EAAAA,EAAcC,KAAK,2BAA4B,CAC3CC,KAAM,WACNC,KAAMZ,EACNa,MAAOZ,EACPa,KAAMZ,GACP,CACCa,QAAS,CACLC,cAAe,UAAYZ,KAE/Ba,MAAKC,IAEiB,IAAlBA,EAAIC,KAAKC,OACTb,QAAQC,IAAI,WAAYU,EAAIC,KAAKA,KAAKE,QACtC7B,EAAaW,MAAQe,EAAIC,KAAKA,KAAKE,OAEnC5B,EAAeU,MAAQe,EAAIC,KAAKA,KAAKG,MACzC,IAGDC,OAAMC,IACLjB,QAAQC,IAAIgB,GACZC,EAAAA,GAAUD,MAAM,yBAAyB,GAC3C,EAIAE,EAAkBA,KAChBrC,EAAWc,QACXT,EAAYS,MAAQ,GAExBJ,EAAgBL,EAAYS,MAAOR,EAASQ,MAAOd,EAAWc,MAAM,EAIlEwB,EAAoBf,IACtBlB,EAAYS,MAAQS,EACpBb,EAAgBL,EAAYS,MAAOR,EAASQ,MAAOd,EAAWc,MAAM,GAGxEyB,EAAAA,EAAAA,KAAU,IAAM7B,EAAgB,EAAG,IAAK,OACxC8B,EAAAA,EAAAA,IAAMxC,EAAYqC,GAIlB,MAAMI,GAAoBC,EAAAA,EAAAA,KAAS,KAC/BxB,QAAQC,IAAI,WACLhB,EAAaW,MAAM6B,QAAOC,GAA4C,IAAhCA,EAASC,yBAI1DL,EAAAA,EAAAA,IAAMC,GAAoBK,IACtB5B,QAAQC,IAAI,SAAU2B,EAAOC,QAC7B3C,EAAeU,MAAQgC,EAAOC,MAAM,IAYxC,MAAMC,GAAmBxC,EAAAA,EAAAA,IAAO,oBAAoBP,EAAAA,EAAAA,IAAI,OAGlDgD,EAAuBL,IAErBI,IACAA,EAAiBlC,MAAQ,CACrBW,KAAMmB,EAASnB,KACfyB,OAAQC,EAAQ,MAChBC,MAAO,OACPC,GAAIT,EAASU,QAAU,KAG/BpC,QAAQC,IAAI,SAAUyB,EAASnB,KAAK,EAGlC8B,EAAcA,CAACC,EAAKC,KACtBvC,QAAQC,IAAIqC,EAAKC,EAAM,E,qhDCtJ3B,MAAMC,EAAc,EAEpB,O", "sources": ["webpack://wccsfront/./src/components/ChatArchive/EmployeeChat/EmployeeList.vue", "webpack://wccsfront/./src/components/ChatArchive/EmployeeChat/EmployeeList.vue?3518"], "sourcesContent": ["<!-- TODO：\r\n固定搜索框的宽度 -->\r\n<template>\r\n    <div class=\"mainContainer-employeelist\">\r\n        <div class=\"search\" style=\"width: 800px;\">\r\n            <el-input placeholder=\"搜索存档人员\" v-model=\"searchWord\" clearable class=\"search-input\">\r\n                <template #prefix>\r\n                    <el-icon class=\"search-icon\">\r\n                        <Search />\r\n                    </el-icon>\r\n                </template>\r\n            </el-input>\r\n        </div>\r\n\r\n        <div class=\"lable-navigator\">\r\n            <el-tabs v-model=\"activeName\" class=\"employeeList-tabs\" @tab-click=\"handleClick\">\r\n                <el-tab-pane label=\"存档人员\" name=\"first\">\r\n                    <div class=\"employee-card\" v-for=\"employee in archivedEmployees\" :key=\"employee.name\"\r\n                        @click=\"handleEmployeeClick(employee)\">\r\n                        <div class=\"profile\">\r\n                            <img class=\"staff-avatar\" src=\"@/assets/人员头像.png\" alt=\"人员头像\">\r\n                        </div>\r\n                        <div class=\"info\">\r\n                            <p class=\"name\">{{ employee.name }}</p>\r\n                            <p class=\"lable\">存档员工</p>\r\n                        </div>\r\n                        <div class=\"mark\">\r\n                            <el-tag size=\"small\" type=\"primary\" effect=\"plain\">存档</el-tag>\r\n                        </div>\r\n                    </div>\r\n                </el-tab-pane>\r\n                <!-- <el-tab-pane label=\"Config\" name=\"second\">Config</el-tab-pane>\r\n                <el-tab-pane label=\"Role\" name=\"third\">Role</el-tab-pane>\r\n                <el-tab-pane label=\"Task\" name=\"fourth\">Task</el-tab-pane> -->\r\n            </el-tabs>\r\n        </div>\r\n\r\n        <div class=\"pageContainer\">\r\n            <el-pagination v-model:current-page=\"currentPage\" background :size=\"size\" layout=\"total, prev, pager, next\"\r\n                :total=\"totalEmployees\" :page-size=\"pageSize\" :pager-count=\"3\" small @current-change=\"handlePageChange\">\r\n            </el-pagination>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { Search } from '@element-plus/icons-vue';\r\nimport { ref, onMounted, watch, inject, computed } from 'vue';\r\nimport axiosInstance from '@/axiosConfig.js'; \r\nimport { ElMessage } from 'element-plus';\r\n\r\nconst searchWord = ref(''); // searchWord 响应搜索框输入数据\r\nconst activeName = ref('first') // activeName 响应导航栏的tab切换，默认切换第一个tab\r\n\r\nconst employeeList = ref([]); // employeeList 员工列表\r\nconst totalEmployees = ref('');//employeeList 分页组件展示的Total值\r\n\r\nconst currentPage = ref(1); // currentPage 响应当前页码，默认第一页\r\nconst pageSize = ref(100); // pageSize 响应每页数量，默认100条\r\n\r\nconst currentComponent_List = inject('currentComponent_List')\r\nconst currentComponent_ChatList = inject('currentComponent_ChatList')\r\n\r\n// -------------------------------------------------------------------------- 获取员工列表与搜索实现\r\nconst getEmployeeList = (curPage, maxPageNum, searchName) => {\r\n\r\n    currentComponent_List.value = 'EmployeeList';\r\n    currentComponent_ChatList.value = 'EmployeeChatList';\r\n\r\n    // 获取存档人员列表\r\n    const jwt_token = localStorage.getItem('access_token')\r\n\r\n    console.log('获取员工列表接口调用')\r\n\r\n    axiosInstance.post('/api/chatmessage/chatter', {\r\n        type: \"employee\", \r\n        page: curPage,\r\n        limit: maxPageNum,\r\n        name: searchName\r\n    }, {\r\n        headers: {\r\n            Authorization: 'Bearer ' + jwt_token\r\n        },\r\n    },).then(res => {\r\n\r\n        if (res.data.code === 0) {\r\n            console.log('成功获取员工列表', res.data.data.result)\r\n            employeeList.value = res.data.data.result;\r\n            //此处的数量是所获取的人员列表的数量，非存档员工列表数量\r\n            totalEmployees.value = res.data.data.total;\r\n        }\r\n\r\n\r\n    }).catch(error => {\r\n        console.log(error);\r\n        ElMessage.error('获取存档人员列表失败，请检查网络或联系管理员');\r\n    });\r\n}\r\n\r\n//搜索框变化时调用\r\nconst filterEmployees = () => {\r\n    if (searchWord.value) {\r\n        currentPage.value = 1; \r\n    }\r\n    getEmployeeList(currentPage.value, pageSize.value, searchWord.value);\r\n}\r\n\r\n//页码变化时调用\r\nconst handlePageChange = (page) => {\r\n    currentPage.value = page;\r\n    getEmployeeList(currentPage.value, pageSize.value, searchWord.value);\r\n}\r\n\r\nonMounted(() => getEmployeeList(1, 100, ''));\r\nwatch(searchWord, filterEmployees);\r\n\r\n\r\n// -------------------------------------------------------------------------- 计算属性：仅展示存档人员\r\nconst archivedEmployees = computed(() => {\r\n    console.log('已过滤存档员工')\r\n    return employeeList.value.filter(employee => employee.sessionArchiveFlag === 1);\r\n});\r\n\r\n// 更新总数计算\r\nwatch(archivedEmployees, (newVal) => {\r\n    console.log('存档员工总数', newVal.length)\r\n    totalEmployees.value = newVal.length;\r\n});\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n// -------------------------------------------------------------------------- 点击员工卡片，传递员工信息（名称，头像）至 EmployeeChatList 组件\r\nconst selectedEmployee = inject('selectedEmployee', ref(null));\r\n\r\n// 员工卡片点击时调用\r\nconst handleEmployeeClick = (employee) => {\r\n    // 记录选中的员工信息，没有选中时为ref(null)\r\n    if (selectedEmployee) {\r\n        selectedEmployee.value = {\r\n            name: employee.name,\r\n            avatar: require('@/assets/人员头像.png'),\r\n            label: '存档员工',\r\n            id: employee.userid || '',\r\n        };\r\n    }\r\n    console.log('已选中员工:', employee.name);\r\n};\r\n\r\nconst handleClick = (tab, event) => {\r\n    console.log(tab, event)\r\n}\r\n\r\n\r\n\r\n</script>\r\n\r\n<style>\r\n.mainContainer-employeelist {\r\n    margin: 0;\r\n    height: calc(100vh - 6.969rem);\r\n    background-color: #ffffff;\r\n    display: flex;\r\n    flex-direction: column;\r\n    width: 15.625rem;\r\n    padding: 1rem 0.75rem;\r\n    border-right: 0.0625rem solid #e6e6e6;\r\n    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.02);\r\n}\r\n\r\n.search {\r\n    margin-bottom: 0.3125rem;\r\n}\r\n\r\n.search-input {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.search-input:hover .el-input__wrapper {\r\n    box-shadow: 0 0 0 0.0625rem #e6e6e6 inset;\r\n}\r\n\r\n.search-icon {\r\n    color: #909399;\r\n}\r\n\r\n.lable-navigator {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar {\r\n    width: 0.375rem;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar-thumb {\r\n    background-color: #e0e0e0;\r\n    border-radius: 0.1875rem;\r\n}\r\n\r\n.lable-navigator::-webkit-scrollbar-track {\r\n    background-color: transparent;\r\n}\r\n\r\n.employee-card {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    height: 4rem;\r\n    padding: 0 0.75rem;\r\n    margin: 0.25rem 0;\r\n    border-radius: 0.5rem;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.employee-card:hover {\r\n    background-color: #f5f7fa;\r\n    transform: translateX(0.125rem);\r\n}\r\n\r\n.employee-card .profile {\r\n    /* background: linear-gradient(135deg, #1890ff, #0960bd); */\r\n    /* color: #ffffff; */\r\n    width: 2.5rem;\r\n    height: 2.5rem;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    border-radius: 0.5rem;\r\n    margin-right: 0.75rem;\r\n    /* font-weight: 500; */\r\n    box-shadow: 0 0.125rem 0.25rem rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n.employee-card .profile img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.employee-card .info {\r\n    flex: 1;\r\n}\r\n\r\n.employee-card .info .name {\r\n    font-size: 0.875rem;\r\n    font-weight: 500;\r\n    color: #2c3e50;\r\n    margin-bottom: 0.25rem;\r\n}\r\n\r\n.employee-card .info .lable {\r\n    font-size: 0.75rem;\r\n    color: #909399;\r\n}\r\n\r\n.employee-card .mark {\r\n    margin-left: 0.5rem;\r\n}\r\n\r\n.pageContainer {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 0.75rem 0;\r\n    margin-top: 0.5rem;\r\n    border-top: 0.0625rem solid #f0f0f0;\r\n    width: 100%;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination) {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    padding: 0 0.5rem;\r\n    font-size: 0.8125rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pagination__total) {\r\n    min-width: auto;\r\n    margin-right: 0.5rem;\r\n    font-size: 0.8125rem;\r\n    color: #606266;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager) {\r\n    margin: 0;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager li) {\r\n    min-width: 1.5rem;\r\n    height: 1.5rem;\r\n    line-height: 1.5rem;\r\n    font-weight: normal;\r\n    margin: 0 0.125rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-pager li.active) {\r\n    background-color: #1890ff;\r\n    color: #ffffff;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination button) {\r\n    min-width: 1.5rem;\r\n    height: 1.5rem;\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .btn-prev),\r\n.pageContainer :deep(.el-pagination .btn-next) {\r\n    margin: 0 0.125rem;\r\n    background-color: #f4f4f5;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .el-icon) {\r\n    font-size: 0.75rem;\r\n}\r\n\r\n.pageContainer :deep(.el-pagination .more) {\r\n    background-color: transparent;\r\n}\r\n\r\n.employeeList-tabs {\r\n    height: 100%;\r\n}\r\n\r\n.employeeList-tabs :deep(.el-tabs__header) {\r\n    margin-bottom: 1rem;\r\n}\r\n\r\n.employeeList-tabs :deep(.el-tabs__nav-wrap::after) {\r\n    height: 0.0625rem;\r\n    background-color: #f0f0f0;\r\n}\r\n\r\n.employeeList-tabs :deep(.el-tabs__item) {\r\n    font-size: 0.875rem;\r\n    color: #606266;\r\n    padding: 0 1rem;\r\n}\r\n\r\n.employeeList-tabs :deep(.el-tabs__item.is-active) {\r\n    color: #1890ff;\r\n    font-weight: 500;\r\n}\r\n\r\n.employeeList-tabs :deep(.el-tabs__active-bar) {\r\n    background-color: #1890ff;\r\n    height: 0.125rem;\r\n}\r\n</style>\r\n", "import script from \"./EmployeeList.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./EmployeeList.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./EmployeeList.vue?vue&type=style&index=0&id=6a746187&lang=css\"\n\nconst __exports__ = script;\n\nexport default __exports__"], "names": ["searchWord", "ref", "activeName", "employeeList", "totalEmployees", "currentPage", "pageSize", "currentComponent_List", "inject", "currentComponent_ChatList", "getEmployeeList", "curPage", "maxPageNum", "searchName", "value", "jwt_token", "localStorage", "getItem", "console", "log", "axiosInstance", "post", "type", "page", "limit", "name", "headers", "Authorization", "then", "res", "data", "code", "result", "total", "catch", "error", "ElMessage", "filterEmployees", "handlePageChange", "onMounted", "watch", "archivedEmployees", "computed", "filter", "employee", "sessionArchiveFlag", "newVal", "length", "selectedEmployee", "handleEmployeeClick", "avatar", "require", "label", "id", "userid", "handleClick", "tab", "event", "__exports__"], "sourceRoot": ""}