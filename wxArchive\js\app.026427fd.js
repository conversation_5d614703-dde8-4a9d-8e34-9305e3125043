(function(){"use strict";var e={1502:function(e,t,n){var o=n(5130),r=n(6768);function i(e,t,n,o,i,a){const u=(0,r.g2)("router-view");return(0,r.uX)(),(0,r.Wv)(u)}var a={name:"MainApp"},u=n(1241);const c=(0,u.A)(a,[["render",i]]);var l=c,s=n(5835),f=(n(4188),n(7477)),m=n(1387);const d=[{path:"/wxArchive",name:"Home",component:()=>Promise.all([n.e(314),n.e(843),n.e(849)]).then(n.bind(n,539)),meta:{requiresAuth:!0}},{path:"/login",name:"Login",component:()=>Promise.all([n.e(314),n.e(954)]).then(n.bind(n,954))},{path:"/functionbarfold",name:"FunctionBarFold",component:()=>n.e(43).then(n.bind(n,1043)),meta:{requiresAuth:!0}},{path:"/breadcrumb",name:"Breadcrumb",component:()=>n.e(623).then(n.bind(n,7623)),meta:{requiresAuth:!0}},{path:"/employeelist",name:"EmployeeList",component:()=>Promise.all([n.e(314),n.e(752)]).then(n.bind(n,9752)),meta:{requiresAuth:!0}},{path:"/customerlist",name:"CustomerList",component:()=>Promise.all([n.e(314),n.e(978)]).then(n.bind(n,8978)),meta:{requiresAuth:!0}},{path:"/grouplist",name:"GroupList",component:()=>Promise.all([n.e(314),n.e(124)]).then(n.bind(n,8124)),meta:{requiresAuth:!0}},{path:"/employeechatlist",name:"EmployeeChatList",component:()=>Promise.all([n.e(314),n.e(843),n.e(306)]).then(n.bind(n,6843)),meta:{requiresAuth:!0}},{path:"/customerchatlist",name:"CustomerChatList",component:()=>Promise.all([n.e(314),n.e(67)]).then(n.bind(n,9067)),meta:{requiresAuth:!0}},{path:"/voice-test",name:"VoiceTest",component:()=>n.e(548).then(n.bind(n,9167)),meta:{requiresAuth:!1}}],h=(0,m.aE)({history:(0,m.LA)(),routes:d});h.beforeEach(((e,t,n)=>{const o=localStorage.getItem("user"),r=localStorage.getItem("expireTime");console.log("全局路由守卫--获取localStorage的user",o),console.log("全局路由守卫--获取localStorage的expireTime",r),console.log("全局路由守卫--获取当前时间",Date.now()),e.meta.requiresAuth?o?r<Date.now()?(console.log("全局路由守卫--已登录，已过期",o),localStorage.removeItem("user"),localStorage.removeItem("expireTime"),n("/login")):(console.log("全局路由守卫--已登录，未过期",o),n()):(console.log("全局路由守卫--未登录",o),n("/login")):"/login"===e.path&&o&&r>Date.now()?(console.log("全局路由守卫--已登录，未过期，访问登录页，跳转到首页",o),n("/")):(console.log("全局路由守卫--无需登录或正常访问",o),n())}));var p=h,b=n(384);const g=(0,o.Ef)(l);g.use(s.A),g.use(p),g.use(f),g.use(b.Fc),g.mount("#app")}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={exports:{}};return e[o].call(i.exports,i,i.exports,n),i.exports}n.m=e,function(){var e=[];n.O=function(t,o,r,i){if(!o){var a=1/0;for(s=0;s<e.length;s++){o=e[s][0],r=e[s][1],i=e[s][2];for(var u=!0,c=0;c<o.length;c++)(!1&i||a>=i)&&Object.keys(n.O).every((function(e){return n.O[e](o[c])}))?o.splice(c--,1):(u=!1,i<a&&(a=i));if(u){e.splice(s--,1);var l=r();void 0!==l&&(t=l)}}return t}i=i||0;for(var s=e.length;s>0&&e[s-1][2]>i;s--)e[s]=e[s-1];e[s]=[o,r,i]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}}(),function(){n.f={},n.e=function(e){return Promise.all(Object.keys(n.f).reduce((function(t,o){return n.f[o](e,t),t}),[]))}}(),function(){n.u=function(e){return"js/"+e+"."+{43:"2dcb529c",67:"743f96a4",124:"7a0533b6",314:"1ce42797",548:"86d5f086",623:"e5699fb2",752:"a48a0bd1",843:"75dbb687",849:"d3c12993",954:"90097c6e",978:"039fe18f"}[e]+".js"}}(),function(){n.miniCssF=function(e){return"css/"+e+"."+{43:"773fb6c9",67:"11dd6865",124:"004fe65c",306:"c370a175",548:"c6a5577e",623:"95bec1f8",752:"b512a6d4",849:"dc5bca96",954:"c0c78dbf",978:"ee8bdee3"}[e]+".css"}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="wccsfront:";n.l=function(o,r,i,a){if(e[o])e[o].push(r);else{var u,c;if(void 0!==i)for(var l=document.getElementsByTagName("script"),s=0;s<l.length;s++){var f=l[s];if(f.getAttribute("src")==o||f.getAttribute("data-webpack")==t+i){u=f;break}}u||(c=!0,u=document.createElement("script"),u.charset="utf-8",u.timeout=120,n.nc&&u.setAttribute("nonce",n.nc),u.setAttribute("data-webpack",t+i),u.src=o),e[o]=[r];var m=function(t,n){u.onerror=u.onload=null,clearTimeout(d);var r=e[o];if(delete e[o],u.parentNode&&u.parentNode.removeChild(u),r&&r.forEach((function(e){return e(n)})),t)return t(n)},d=setTimeout(m.bind(null,void 0,{type:"timeout",target:u}),12e4);u.onerror=m.bind(null,u.onerror),u.onload=m.bind(null,u.onload),c&&document.head.appendChild(u)}}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){n.p="/wxArchive/"}(),function(){if("undefined"!==typeof document){var e=function(e,t,o,r,i){var a=document.createElement("link");a.rel="stylesheet",a.type="text/css",n.nc&&(a.nonce=n.nc);var u=function(n){if(a.onerror=a.onload=null,"load"===n.type)r();else{var o=n&&n.type,u=n&&n.target&&n.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+o+": "+u+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=o,c.request=u,a.parentNode&&a.parentNode.removeChild(a),i(c)}};return a.onerror=a.onload=u,a.href=t,o?o.parentNode.insertBefore(a,o.nextSibling):document.head.appendChild(a),a},t=function(e,t){for(var n=document.getElementsByTagName("link"),o=0;o<n.length;o++){var r=n[o],i=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(i===e||i===t))return r}var a=document.getElementsByTagName("style");for(o=0;o<a.length;o++){r=a[o],i=r.getAttribute("data-href");if(i===e||i===t)return r}},o=function(o){return new Promise((function(r,i){var a=n.miniCssF(o),u=n.p+a;if(t(a,u))return r();e(o,u,null,r,i)}))},r={524:0};n.f.miniCss=function(e,t){var n={43:1,67:1,124:1,306:1,548:1,623:1,752:1,849:1,954:1,978:1};r[e]?t.push(r[e]):0!==r[e]&&n[e]&&t.push(r[e]=o(e).then((function(){r[e]=0}),(function(t){throw delete r[e],t})))}}}(),function(){var e={524:0};n.f.j=function(t,o){var r=n.o(e,t)?e[t]:void 0;if(0!==r)if(r)o.push(r[2]);else if(306!=t){var i=new Promise((function(n,o){r=e[t]=[n,o]}));o.push(r[2]=i);var a=n.p+n.u(t),u=new Error,c=function(o){if(n.o(e,t)&&(r=e[t],0!==r&&(e[t]=void 0),r)){var i=o&&("load"===o.type?"missing":o.type),a=o&&o.target&&o.target.src;u.message="Loading chunk "+t+" failed.\n("+i+": "+a+")",u.name="ChunkLoadError",u.type=i,u.request=a,r[1](u)}};n.l(a,c,"chunk-"+t,t)}else e[t]=0},n.O.j=function(t){return 0===e[t]};var t=function(t,o){var r,i,a=o[0],u=o[1],c=o[2],l=0;if(a.some((function(t){return 0!==e[t]}))){for(r in u)n.o(u,r)&&(n.m[r]=u[r]);if(c)var s=c(n)}for(t&&t(o);l<a.length;l++)i=a[l],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return n.O(s)},o=self["webpackChunkwccsfront"]=self["webpackChunkwccsfront"]||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))}();var o=n.O(void 0,[504],(function(){return n(1502)}));o=n.O(o)})();
//# sourceMappingURL=app.026427fd.js.map